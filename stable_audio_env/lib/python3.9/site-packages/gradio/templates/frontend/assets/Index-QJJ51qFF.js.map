{"version": 3, "file": "Index-QJJ51qFF.js", "sources": ["../../../../js/html/shared/HTML.svelte", "../../../../js/html/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\texport let elem_classes: string[] = [];\n\texport let value: string;\n\texport let visible = true;\n\texport let min_height = false;\n\n\tconst dispatch = createEventDispatcher<{ change: undefined }>();\n\n\t$: value, dispatch(\"change\");\n</script>\n\n<div\n\tclass=\"prose {elem_classes.join(' ')}\"\n\tclass:min={min_height}\n\tclass:hide={!visible}\n>\n\t{@html value}\n</div>\n\n<style>\n\t.min {\n\t\tmin-height: var(--size-24);\n\t}\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport HTML from \"./shared/HTML.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { Code as CodeIcon } from \"@gradio/icons\";\n\n\texport let label: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let show_label = false;\n\n\t$: label, gradio.dispatch(\"change\");\n</script>\n\n<Block {visible} {elem_id} {elem_classes} container={false}>\n\t{#if show_label}\n\t\t<span class=\"label-container\">\n\t\t\t<BlockLabel Icon={CodeIcon} {show_label} {label} float={true} />\n\t\t</span>\n\t{/if}\n\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tvariant=\"center\"\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<div class:pending={loading_status?.status === \"pending\"}>\n\t\t<HTML\n\t\t\tmin_height={loading_status && loading_status?.status !== \"complete\"}\n\t\t\t{value}\n\t\t\t{elem_classes}\n\t\t\t{visible}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\tdiv {\n\t\ttransition: 150ms;\n\t}\n\n\t.pending {\n\t\topacity: 0.2;\n\t}\n\n\t.label-container :global(label) {\n\t\ttop: -8px !important;\n\t\tposition: relative !important;\n\t\tleft: -8px !important;\n\t\tbackground: var(--block-background-fill) !important;\n\t\tborder-top: var(--block-label-border-width) solid\n\t\t\tvar(--border-color-primary) !important;\n\t\tborder-left: var(--block-label-border-width) solid\n\t\t\tvar(--border-color-primary) !important;\n\t}\n</style>\n"], "names": ["createEventDispatcher", "ctx", "insert", "target", "div", "anchor", "elem_classes", "$$props", "value", "visible", "min_height", "dispatch", "CodeIcon", "span", "create_if_block", "dirty", "html_changes", "label", "elem_id", "loading_status", "gradio", "show_label", "clear_status_handler"], "mappings": "gXACU,CAAA,sBAAAA,CAAA,SAAqC,8FAYhCC,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,YACxBA,EAAU,CAAA,CAAA,cACRA,EAAO,CAAA,CAAA,UAHrBC,EAMKC,EAAAC,EAAAC,CAAA,cADGJ,EAAK,CAAA,8BAALA,EAAK,CAAA,wBAJEA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,kDACxBA,EAAU,CAAA,CAAA,mBACRA,EAAO,CAAA,CAAA,gDAbT,aAAAK,EAAY,EAAA,EAAAC,GACZ,MAAAC,CAAa,EAAAD,EACb,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,WAAAG,EAAa,EAAK,EAAAH,EAEvB,MAAAI,EAAWX,mMAEPW,EAAS,QAAQ,uzBCiBPC,mCAAsC,6FADzDV,EAEMC,EAAAU,EAAAR,CAAA,qNAHFJ,EAAU,CAAA,GAAAa,EAAAb,CAAA,WAOF,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,4IAML,WAAAA,EAAkB,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,+LAFvCA,EAAc,CAAA,GAAE,SAAW,SAAS,iDAAxDC,EAQKC,EAAAC,EAAAC,CAAA,2BArBAJ,EAAU,CAAA,4HAOFc,EAAA,IAAA,CAAA,WAAAd,KAAO,UAAU,EACvBc,EAAA,IAAA,CAAA,KAAAd,KAAO,IAAI,UACbA,EAAc,CAAA,CAAA,iCAMLc,EAAA,KAAAC,EAAA,WAAAf,EAAkB,CAAA,GAAAA,EAAgB,CAAA,GAAA,SAAW,sHAFvCA,EAAc,CAAA,GAAE,SAAW,SAAS,yQAdJ,yUAfzC,MAAAgB,CAAa,EAAAV,EACb,CAAA,QAAAW,EAAU,EAAE,EAAAX,GACZ,aAAAD,EAAY,EAAA,EAAAC,EACZ,CAAA,QAAAE,EAAU,EAAI,EAAAF,EACd,CAAA,MAAAC,EAAQ,EAAE,EAAAD,GACV,eAAAY,CAA6B,EAAAZ,GAC7B,OAAAa,CAGT,EAAAb,EACS,CAAA,WAAAc,EAAa,EAAK,EAAAd,EAiBL,MAAAe,EAAA,IAAAF,EAAO,SAAS,eAAgBD,CAAc,QAQnDC,EAAO,SAAS,QAAQ,0UAvBjCA,EAAO,SAAS,QAAQ"}