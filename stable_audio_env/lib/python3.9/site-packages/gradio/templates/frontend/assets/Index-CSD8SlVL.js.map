{"version": 3, "file": "Index-CSD8SlVL.js", "sources": ["../../../../js/textbox/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as BaseTextbox } from \"./shared/Textbox.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport TextBox from \"./shared/Textbox.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let gradio: Gradio<{\n\t\tchange: string;\n\t\tsubmit: never;\n\t\tblur: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tfocus: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let label = \"Textbox\";\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let lines: number;\n\texport let placeholder = \"\";\n\texport let show_label: boolean;\n\texport let max_lines: number;\n\texport let type: \"text\" | \"password\" | \"email\" = \"text\";\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let show_copy_button = false;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let value_is_output = false;\n\texport let rtl = false;\n\texport let text_align: \"left\" | \"right\" | undefined = undefined;\n\texport let autofocus = false;\n\texport let autoscroll = true;\n\texport let interactive: boolean;\n\texport let max_length: number | undefined = undefined;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tpadding={container}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\n\t<TextBox\n\t\tbind:value\n\t\tbind:value_is_output\n\t\t{label}\n\t\t{info}\n\t\t{show_label}\n\t\t{lines}\n\t\t{type}\n\t\t{rtl}\n\t\t{text_align}\n\t\tmax_lines={!max_lines ? lines + 1 : max_lines}\n\t\t{placeholder}\n\t\t{show_copy_button}\n\t\t{autofocus}\n\t\t{container}\n\t\t{autoscroll}\n\t\t{max_length}\n\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:submit={() => gradio.dispatch(\"submit\")}\n\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\tdisabled={!interactive}\n\t/>\n</Block>\n"], "names": ["ctx", "dirty", "create_if_block", "gradio", "$$props", "label", "info", "elem_id", "elem_classes", "visible", "value", "lines", "placeholder", "show_label", "max_lines", "type", "container", "scale", "min_width", "show_copy_button", "loading_status", "value_is_output", "rtl", "text_align", "autofocus", "autoscroll", "interactive", "max_length", "clear_status_handler", "change_handler", "e"], "mappings": "k0BA2De,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,wLAFNC,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAD,KAAO,UAAU,EACvBC,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAD,KAAO,IAAI,kBACbA,EAAc,EAAA,CAAA,gIAJfA,EAAc,EAAA,GAAAE,EAAAF,CAAA,wJAmBNA,EAAS,EAAA,EAAeA,EAAS,EAAA,EAArBA,KAAQ,sHAarBA,EAAW,EAAA,6XAhClBA,EAAc,EAAA,kVAmBNA,EAAS,EAAA,EAAeA,EAAS,EAAA,EAArBA,KAAQ,2OAarBA,EAAW,EAAA,gWAnCP,WACPA,EAAS,EAAA,iRAATA,EAAS,EAAA,sKAzCP,OAAAG,CAQT,EAAAC,EACS,CAAA,MAAAC,EAAQ,SAAS,EAAAD,EACjB,CAAA,KAAAE,EAA2B,MAAS,EAAAF,EACpC,CAAA,QAAAG,EAAU,EAAE,EAAAH,GACZ,aAAAI,EAAY,EAAA,EAAAJ,EACZ,CAAA,QAAAK,EAAU,EAAI,EAAAL,EACd,CAAA,MAAAM,EAAQ,EAAE,EAAAN,GACV,MAAAO,CAAa,EAAAP,EACb,CAAA,YAAAQ,EAAc,EAAE,EAAAR,GAChB,WAAAS,CAAmB,EAAAT,GACnB,UAAAU,CAAiB,EAAAV,EACjB,CAAA,KAAAW,EAAsC,MAAM,EAAAX,EAC5C,CAAA,UAAAY,EAAY,EAAI,EAAAZ,EAChB,CAAA,MAAAa,EAAuB,IAAI,EAAAb,EAC3B,CAAA,UAAAc,EAAgC,MAAS,EAAAd,EACzC,CAAA,iBAAAe,EAAmB,EAAK,EAAAf,EACxB,CAAA,eAAAgB,EAA4C,MAAS,EAAAhB,EACrD,CAAA,gBAAAiB,EAAkB,EAAK,EAAAjB,EACvB,CAAA,IAAAkB,EAAM,EAAK,EAAAlB,EACX,CAAA,WAAAmB,EAA2C,MAAS,EAAAnB,EACpD,CAAA,UAAAoB,EAAY,EAAK,EAAApB,EACjB,CAAA,WAAAqB,EAAa,EAAI,EAAArB,GACjB,YAAAsB,CAAoB,EAAAtB,EACpB,CAAA,WAAAuB,EAAiC,MAAS,EAAAvB,EAiB5B,MAAAwB,EAAA,IAAAzB,EAAO,SAAS,eAAgBiB,CAAc,oDAqBrD,MAAAS,EAAA,IAAA1B,EAAO,SAAS,SAAUO,CAAK,QAChCP,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,QAC1BA,EAAO,SAAS,MAAM,IACzB2B,GAAM3B,EAAO,SAAS,SAAU2B,EAAE,MAAM,QACpC3B,EAAO,SAAS,OAAO"}