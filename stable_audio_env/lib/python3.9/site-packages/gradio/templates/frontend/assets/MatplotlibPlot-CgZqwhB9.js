const{SvelteComponent:p,append:_,attr:n,bubble:f,detach:m,element:c,flush:g,init:h,insert:b,listen:j,noop:d,safe_not_equal:q,src_url_equal:v}=window.__gradio__svelte__internal;function w(l){let e,t,i,a,o,s;return{c(){e=c("div"),t=c("img"),v(t.src,i=l[1])||n(t,"src",i),n(t,"alt",a=`${l[0].chart} plot visualising provided data`),n(t,"class","svelte-j1jcu3"),n(e,"data-testid","matplotlib"),n(e,"class","matplotlib layout svelte-j1jcu3")},m(u,r){b(u,e,r),_(e,t),o||(s=j(t,"load",l[2]),o=!0)},p(u,[r]){r&2&&!v(t.src,i=u[1])&&n(t,"src",i),r&1&&a!==(a=`${u[0].chart} plot visualising provided data`)&&n(t,"alt",a)},i:d,o:d,d(u){u&&m(e),o=!1,s()}}}function $(l,e,t){let i,{value:a}=e;function o(s){f.call(this,l,s)}return l.$$set=s=>{"value"in s&&t(0,a=s.value)},l.$$.update=()=>{l.$$.dirty&1&&t(1,i=a?.plot)},[a,i,o]}class y extends p{constructor(e){super(),h(this,e,$,w,q,{value:0})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}}export{y as default};
//# sourceMappingURL=MatplotlibPlot-CgZqwhB9.js.map
