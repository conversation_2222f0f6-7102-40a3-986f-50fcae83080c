const{SvelteComponent:r,attr:d,create_slot:h,detach:m,element:w,flush:u,get_all_dirty_from_scope:g,get_slot_changes:v,init:b,insert:q,safe_not_equal:C,set_style:a,toggle_class:o,transition_in:I,transition_out:S,update_slot_base:j}=window.__gradio__svelte__internal;function k(n){let e,l;const f=n[4].default,i=h(f,n,n[3],null);return{c(){e=w("div"),i&&i.c(),d(e,"class","form svelte-sfqy0y"),o(e,"hidden",!n[0]),a(e,"flex-grow",n[1]),a(e,"min-width",`calc(min(${n[2]}px, 100%))`)},m(t,s){q(t,e,s),i&&i.m(e,null),l=!0},p(t,[s]){i&&i.p&&(!l||s&8)&&j(i,f,t,t[3],l?v(f,t[3],s,null):g(t[3]),null),(!l||s&1)&&o(e,"hidden",!t[0]),s&2&&a(e,"flex-grow",t[1]),s&4&&a(e,"min-width",`calc(min(${t[2]}px, 100%))`)},i(t){l||(I(i,t),l=!0)},o(t){S(i,t),l=!1},d(t){t&&m(e),i&&i.d(t)}}}function y(n,e,l){let{$$slots:f={},$$scope:i}=e,{visible:t=!0}=e,{scale:s=null}=e,{min_width:c=0}=e;return n.$$set=_=>{"visible"in _&&l(0,t=_.visible),"scale"in _&&l(1,s=_.scale),"min_width"in _&&l(2,c=_.min_width),"$$scope"in _&&l(3,i=_.$$scope)},[t,s,c,i,f]}class z extends r{constructor(e){super(),b(this,e,y,k,C,{visible:0,scale:1,min_width:2})}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),u()}get scale(){return this.$$.ctx[1]}set scale(e){this.$$set({scale:e}),u()}get min_width(){return this.$$.ctx[2]}set min_width(e){this.$$set({min_width:e}),u()}}export{z as default};
//# sourceMappingURL=Index-DDCF2BFd.js.map
