import{b as Te}from"./index-BQPjLIsY.js";/* empty css                                                   */const{SvelteComponent:Ue,append:A,attr:S,detach:se,element:T,flush:M,init:Le,insert:oe,noop:te,safe_not_equal:Oe,set_data:j,set_style:X,space:Y,text:O,toggle_class:ne}=window.__gradio__svelte__internal,{onMount:Ie,createEventDispatcher:Ne,onDestroy:Me}=window.__gradio__svelte__internal;function le(t){let e,l,n,r,d=I(t[2])+"",o,u,s,a,p=t[2].orig_name+"",_;return{c(){e=T("div"),l=T("span"),n=T("div"),r=T("progress"),o=O(d),s=Y(),a=T("span"),_=O(p),X(r,"visibility","hidden"),X(r,"height","0"),X(r,"width","0"),r.value=u=I(t[2]),S(r,"max","100"),S(r,"class","svelte-1vsfomn"),S(n,"class","progress-bar svelte-1vsfomn"),S(a,"class","file-name svelte-1vsfomn"),S(e,"class","file svelte-1vsfomn")},m(h,c){oe(h,e,c),A(e,l),A(l,n),A(n,r),A(r,o),A(e,s),A(e,a),A(a,_)},p(h,c){c&4&&d!==(d=I(h[2])+"")&&j(o,d),c&4&&u!==(u=I(h[2]))&&(r.value=u),c&4&&p!==(p=h[2].orig_name+"")&&j(_,p)},d(h){h&&se(e)}}}function je(t){let e,l,n,r=t[0].length+"",d,o,u=t[0].length>1?"files":"file",s,a,p,_=t[2]&&le(t);return{c(){e=T("div"),l=T("span"),n=O("Uploading "),d=O(r),o=Y(),s=O(u),a=O("..."),p=Y(),_&&_.c(),S(l,"class","uploading svelte-1vsfomn"),S(e,"class","wrap svelte-1vsfomn"),ne(e,"progress",t[1])},m(h,c){oe(h,e,c),A(e,l),A(l,n),A(l,d),A(l,o),A(l,s),A(l,a),A(e,p),_&&_.m(e,null)},p(h,[c]){c&1&&r!==(r=h[0].length+"")&&j(d,r),c&1&&u!==(u=h[0].length>1?"files":"file")&&j(s,u),h[2]?_?_.p(h,c):(_=le(h),_.c(),_.m(e,null)):_&&(_.d(1),_=null),c&2&&ne(e,"progress",h[1])},i:te,o:te,d(h){h&&se(e),_&&_.d()}}}function I(t){return t.progress*100/(t.size||0)||0}function Je(t){let e=0;return t.forEach(l=>{e+=I(l)}),document.documentElement.style.setProperty("--upload-progress-width",(e/t.length).toFixed(2)+"%"),e/t.length}function Re(t,e,l){let{upload_id:n}=e,{root:r}=e,{files:d}=e,{stream_handler:o}=e,u,s=!1,a,p,_=d.map(f=>({...f,progress:0}));const h=Ne();function c(f,g){l(0,_=_.map(W=>(W.orig_name===f&&(W.progress+=g),W)))}return Ie(async()=>{if(u=await o(new URL(`${r}/upload_progress?upload_id=${n}`)),u==null)throw new Error("Event source is not defined");u.onmessage=async function(f){const g=JSON.parse(f.data);s||l(1,s=!0),g.msg==="done"?(u?.close(),h("done")):(l(7,a=g),c(g.orig_name,g.chunk_size))}}),Me(()=>{(u!=null||u!=null)&&u.close()}),t.$$set=f=>{"upload_id"in f&&l(3,n=f.upload_id),"root"in f&&l(4,r=f.root),"files"in f&&l(5,d=f.files),"stream_handler"in f&&l(6,o=f.stream_handler)},t.$$.update=()=>{t.$$.dirty&1&&Je(_),t.$$.dirty&129&&l(2,p=a||_[0])},[_,s,p,n,r,d,o,a]}class Be extends Ue{constructor(e){super(),Le(this,e,Re,je,Oe,{upload_id:3,root:4,files:5,stream_handler:6})}get upload_id(){return this.$$.ctx[3]}set upload_id(e){this.$$set({upload_id:e}),M()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),M()}get files(){return this.$$.ctx[5]}set files(e){this.$$set({files:e}),M()}get stream_handler(){return this.$$.ctx[6]}set stream_handler(e){this.$$set({stream_handler:e}),M()}}const{SvelteComponent:Ge,append:ie,attr:v,binding_callbacks:He,bubble:q,check_outros:ae,create_component:Ke,create_slot:ue,destroy_component:Qe,detach:J,element:Z,empty:fe,flush:k,get_all_dirty_from_scope:de,get_slot_changes:_e,group_outros:ce,init:Ve,insert:R,listen:z,mount_component:Xe,prevent_default:D,run_all:Ye,safe_not_equal:Ze,set_style:he,space:xe,stop_propagation:P,toggle_class:b,transition_in:F,transition_out:U,update_slot_base:ge}=window.__gradio__svelte__internal,{createEventDispatcher:$e,tick:et,getContext:ut}=window.__gradio__svelte__internal;function tt(t){let e,l,n,r,d,o,u,s,a,p,_;const h=t[27].default,c=ue(h,t,t[26],null);return{c(){e=Z("button"),c&&c.c(),l=xe(),n=Z("input"),v(n,"aria-label","file upload"),v(n,"data-testid","file-upload"),v(n,"type","file"),v(n,"accept",r=t[16]||void 0),n.multiple=d=t[6]==="multiple"||void 0,v(n,"webkitdirectory",o=t[6]==="directory"||void 0),v(n,"mozdirectory",u=t[6]==="directory"||void 0),v(n,"class","svelte-1x5qevo"),v(e,"tabindex",s=t[9]?-1:0),v(e,"class","svelte-1x5qevo"),b(e,"hidden",t[9]),b(e,"center",t[4]),b(e,"boundedheight",t[3]),b(e,"flex",t[5]),b(e,"disable_click",t[7]),he(e,"height","100%")},m(f,g){R(f,e,g),c&&c.m(e,null),ie(e,l),ie(e,n),t[35](n),a=!0,p||(_=[z(n,"change",t[18]),z(e,"drag",P(D(t[28]))),z(e,"dragstart",P(D(t[29]))),z(e,"dragend",P(D(t[30]))),z(e,"dragover",P(D(t[31]))),z(e,"dragenter",P(D(t[32]))),z(e,"dragleave",P(D(t[33]))),z(e,"drop",P(D(t[34]))),z(e,"click",t[13]),z(e,"drop",t[19]),z(e,"dragenter",t[17]),z(e,"dragleave",t[17])],p=!0)},p(f,g){c&&c.p&&(!a||g[0]&67108864)&&ge(c,h,f,f[26],a?_e(h,f[26],g,null):de(f[26]),null),(!a||g[0]&65536&&r!==(r=f[16]||void 0))&&v(n,"accept",r),(!a||g[0]&64&&d!==(d=f[6]==="multiple"||void 0))&&(n.multiple=d),(!a||g[0]&64&&o!==(o=f[6]==="directory"||void 0))&&v(n,"webkitdirectory",o),(!a||g[0]&64&&u!==(u=f[6]==="directory"||void 0))&&v(n,"mozdirectory",u),(!a||g[0]&512&&s!==(s=f[9]?-1:0))&&v(e,"tabindex",s),(!a||g[0]&512)&&b(e,"hidden",f[9]),(!a||g[0]&16)&&b(e,"center",f[4]),(!a||g[0]&8)&&b(e,"boundedheight",f[3]),(!a||g[0]&32)&&b(e,"flex",f[5]),(!a||g[0]&128)&&b(e,"disable_click",f[7])},i(f){a||(F(c,f),a=!0)},o(f){U(c,f),a=!1},d(f){f&&J(e),c&&c.d(f),t[35](null),p=!1,Ye(_)}}}function nt(t){let e,l,n=!t[9]&&re(t);return{c(){n&&n.c(),e=fe()},m(r,d){n&&n.m(r,d),R(r,e,d),l=!0},p(r,d){r[9]?n&&(ce(),U(n,1,1,()=>{n=null}),ae()):n?(n.p(r,d),d[0]&512&&F(n,1)):(n=re(r),n.c(),F(n,1),n.m(e.parentNode,e))},i(r){l||(F(n),l=!0)},o(r){U(n),l=!1},d(r){r&&J(e),n&&n.d(r)}}}function lt(t){let e,l,n,r,d;const o=t[27].default,u=ue(o,t,t[26],null);return{c(){e=Z("button"),u&&u.c(),v(e,"tabindex",l=t[9]?-1:0),v(e,"class","svelte-1x5qevo"),b(e,"hidden",t[9]),b(e,"center",t[4]),b(e,"boundedheight",t[3]),b(e,"flex",t[5]),he(e,"height","100%")},m(s,a){R(s,e,a),u&&u.m(e,null),n=!0,r||(d=z(e,"click",t[12]),r=!0)},p(s,a){u&&u.p&&(!n||a[0]&67108864)&&ge(u,o,s,s[26],n?_e(o,s[26],a,null):de(s[26]),null),(!n||a[0]&512&&l!==(l=s[9]?-1:0))&&v(e,"tabindex",l),(!n||a[0]&512)&&b(e,"hidden",s[9]),(!n||a[0]&16)&&b(e,"center",s[4]),(!n||a[0]&8)&&b(e,"boundedheight",s[3]),(!n||a[0]&32)&&b(e,"flex",s[5])},i(s){n||(F(u,s),n=!0)},o(s){U(u,s),n=!1},d(s){s&&J(e),u&&u.d(s),r=!1,d()}}}function re(t){let e,l;return e=new Be({props:{root:t[8],upload_id:t[14],files:t[15],stream_handler:t[11]}}),{c(){Ke(e.$$.fragment)},m(n,r){Xe(e,n,r),l=!0},p(n,r){const d={};r[0]&256&&(d.root=n[8]),r[0]&16384&&(d.upload_id=n[14]),r[0]&32768&&(d.files=n[15]),r[0]&2048&&(d.stream_handler=n[11]),e.$set(d)},i(n){l||(F(e.$$.fragment,n),l=!0)},o(n){U(e.$$.fragment,n),l=!1},d(n){Qe(e,n)}}}function it(t){let e,l,n,r;const d=[lt,nt,tt],o=[];function u(s,a){return s[0]==="clipboard"?0:s[1]&&s[10]?1:2}return e=u(t),l=o[e]=d[e](t),{c(){l.c(),n=fe()},m(s,a){o[e].m(s,a),R(s,n,a),r=!0},p(s,a){let p=e;e=u(s),e===p?o[e].p(s,a):(ce(),U(o[p],1,1,()=>{o[p]=null}),ae(),l=o[e],l?l.p(s,a):(l=o[e]=d[e](s),l.c()),F(l,1),l.m(n.parentNode,n))},i(s){r||(F(l),r=!0)},o(s){U(l),r=!1},d(s){s&&J(n),o[e].d(s)}}}function rt(t,e,l){if(!t||t==="*"||t==="file/*"||Array.isArray(t)&&t.some(r=>r==="*"||r==="file/*"))return!0;let n;if(typeof t=="string")n=t.split(",").map(r=>r.trim());else if(Array.isArray(t))n=t;else return!1;return n.includes(e)||n.some(r=>{const[d]=r.split("/").map(o=>o.trim());return r.endsWith("/*")&&l.startsWith(d+"/")})}function st(t,e,l){let n,{$$slots:r={},$$scope:d}=e,{filetype:o=null}=e,{dragging:u=!1}=e,{boundedheight:s=!0}=e,{center:a=!0}=e,{flex:p=!0}=e,{file_count:_="single"}=e,{disable_click:h=!1}=e,{root:c}=e,{hidden:f=!1}=e,{format:g="file"}=e,{uploading:W=!1}=e,{hidden_upload:C=null}=e,{show_progress:x=!0}=e,{max_file_size:B=null}=e,{upload:G}=e,{stream_handler:$}=e,H,K,L,ee=null;const me=()=>{if(typeof navigator<"u"){const i=navigator.userAgent.toLowerCase();return i.indexOf("iphone")>-1||i.indexOf("ipad")>-1}return!1},E=$e(),pe=["image","video","audio","text","file"],Q=i=>n&&i.startsWith(".")?(ee=!0,i):n&&i.includes("file/*")?"*":i.startsWith(".")||i.endsWith("/*")?i:pe.includes(i)?i+"/*":"."+i;function be(){l(20,u=!u)}function ye(){navigator.clipboard.read().then(async i=>{for(let m=0;m<i.length;m++){const y=i[m].types.find(w=>w.startsWith("image/"));if(y){i[m].getType(y).then(async w=>{const V=new File([w],`clipboard.${y.replace("image/","")}`);await N([V])});break}}})}function we(){h||C&&(l(2,C.value="",C),C.click())}async function ke(i){await et(),l(14,H=Math.random().toString(36).substring(2,15)),l(1,W=!0);try{const m=await G(i,c,H,B??1/0);return E("load",_==="single"?m?.[0]:m),l(1,W=!1),m||[]}catch(m){return E("error",m.message),l(1,W=!1),[]}}async function N(i){if(!i.length)return;let m=i.map(y=>new File([y],y instanceof File?y.name:"file",{type:y.type}));return n&&ee&&(m=m.filter(y=>ve(y)?!0:(E("error",`Invalid file type: ${y.name}. Only ${o} allowed.`),!1)),m.length===0)?[]:(l(15,K=await Te(m)),await ke(K))}function ve(i){return o?(Array.isArray(o)?o:[o]).some(y=>{const w=Q(y);if(w.startsWith("."))return i.name.toLowerCase().endsWith(w.toLowerCase());if(w==="*")return!0;if(w.endsWith("/*")){const[V]=w.split("/");return i.type.startsWith(V+"/")}return i.type===w}):!0}async function Ae(i){const m=i.target;if(m.files)if(g!="blob")await N(Array.from(m.files));else{if(_==="single"){E("load",m.files[0]);return}E("load",m.files)}}async function ze(i){if(l(20,u=!1),!i.dataTransfer?.files)return;const m=Array.from(i.dataTransfer.files).filter(y=>{const w="."+y.name.split(".").pop();return w&&rt(L,w,y.type)||(w&&Array.isArray(o)?o.includes(w):w===o)?!0:(E("error",`Invalid file type only ${o} allowed.`),!1)});if(g!="blob")await N(m);else{if(_==="single"){E("load",m[0]);return}E("load",m)}}function We(i){q.call(this,t,i)}function Ce(i){q.call(this,t,i)}function Ee(i){q.call(this,t,i)}function Fe(i){q.call(this,t,i)}function qe(i){q.call(this,t,i)}function De(i){q.call(this,t,i)}function Pe(i){q.call(this,t,i)}function Se(i){He[i?"unshift":"push"](()=>{C=i,l(2,C)})}return t.$$set=i=>{"filetype"in i&&l(0,o=i.filetype),"dragging"in i&&l(20,u=i.dragging),"boundedheight"in i&&l(3,s=i.boundedheight),"center"in i&&l(4,a=i.center),"flex"in i&&l(5,p=i.flex),"file_count"in i&&l(6,_=i.file_count),"disable_click"in i&&l(7,h=i.disable_click),"root"in i&&l(8,c=i.root),"hidden"in i&&l(9,f=i.hidden),"format"in i&&l(21,g=i.format),"uploading"in i&&l(1,W=i.uploading),"hidden_upload"in i&&l(2,C=i.hidden_upload),"show_progress"in i&&l(10,x=i.show_progress),"max_file_size"in i&&l(22,B=i.max_file_size),"upload"in i&&l(23,G=i.upload),"stream_handler"in i&&l(11,$=i.stream_handler),"$$scope"in i&&l(26,d=i.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&33554433&&(o==null?l(16,L=null):typeof o=="string"?l(16,L=Q(o)):n&&o.includes("file/*")?l(16,L="*"):(l(0,o=o.map(Q)),l(16,L=o.join(", "))))},l(25,n=me()),[o,W,C,s,a,p,_,h,c,f,x,$,ye,we,H,K,L,be,Ae,ze,u,g,B,G,N,n,d,r,We,Ce,Ee,Fe,qe,De,Pe,Se]}class ft extends Ge{constructor(e){super(),Ve(this,e,st,it,Ze,{filetype:0,dragging:20,boundedheight:3,center:4,flex:5,file_count:6,disable_click:7,root:8,hidden:9,format:21,uploading:1,hidden_upload:2,show_progress:10,max_file_size:22,upload:23,stream_handler:11,paste_clipboard:12,open_file_upload:13,load_files:24},null,[-1,-1])}get filetype(){return this.$$.ctx[0]}set filetype(e){this.$$set({filetype:e}),k()}get dragging(){return this.$$.ctx[20]}set dragging(e){this.$$set({dragging:e}),k()}get boundedheight(){return this.$$.ctx[3]}set boundedheight(e){this.$$set({boundedheight:e}),k()}get center(){return this.$$.ctx[4]}set center(e){this.$$set({center:e}),k()}get flex(){return this.$$.ctx[5]}set flex(e){this.$$set({flex:e}),k()}get file_count(){return this.$$.ctx[6]}set file_count(e){this.$$set({file_count:e}),k()}get disable_click(){return this.$$.ctx[7]}set disable_click(e){this.$$set({disable_click:e}),k()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),k()}get hidden(){return this.$$.ctx[9]}set hidden(e){this.$$set({hidden:e}),k()}get format(){return this.$$.ctx[21]}set format(e){this.$$set({format:e}),k()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),k()}get hidden_upload(){return this.$$.ctx[2]}set hidden_upload(e){this.$$set({hidden_upload:e}),k()}get show_progress(){return this.$$.ctx[10]}set show_progress(e){this.$$set({show_progress:e}),k()}get max_file_size(){return this.$$.ctx[22]}set max_file_size(e){this.$$set({max_file_size:e}),k()}get upload(){return this.$$.ctx[23]}set upload(e){this.$$set({upload:e}),k()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),k()}get paste_clipboard(){return this.$$.ctx[12]}get open_file_upload(){return this.$$.ctx[13]}get load_files(){return this.$$.ctx[24]}}export{ft as U};
//# sourceMappingURL=Upload-1-QDTAlg.js.map
