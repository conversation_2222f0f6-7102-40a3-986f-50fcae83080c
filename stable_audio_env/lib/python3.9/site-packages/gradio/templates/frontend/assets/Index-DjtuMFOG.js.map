{"version": 3, "file": "Index-DjtuMFOG.js", "sources": ["../../../../js/gallery/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BaseGallery } from \"./shared/Gallery.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData, SelectData } from \"@gradio/utils\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport Gallery from \"./shared/Gallery.svelte\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { BaseFileUpload } from \"@gradio/file\";\n\n\texport let loading_status: LoadingStatus;\n\texport let show_label: boolean;\n\texport let label: string;\n\texport let root: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { image: FileData; caption: string | null }[] | null = null;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let selected_index: number | null = null;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let interactive: boolean;\n\texport let show_download_button = false;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tupload: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tprop_change: Record<string, any>;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let show_fullscreen_button = true;\n\n\tconst dispatch = createEventDispatcher();\n\n\t$: no_value = Array.isArray(value) ? value.length === 0 : !value;\n\t$: selected_index, dispatch(\"prop_change\", { selected_index });\n</script>\n\n<Block\n\t{visible}\n\tvariant=\"solid\"\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\theight={typeof height === \"number\" ? height : undefined}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t{#if interactive && no_value}\n\t\t<BaseFileUpload\n\t\t\tvalue={null}\n\t\t\t{root}\n\t\t\t{label}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\tfile_count={\"multiple\"}\n\t\t\tfile_types={[\"image\"]}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={gradio.client.upload}\n\t\t\tstream_handler={gradio.client.stream}\n\t\t\ton:upload={(e) => {\n\t\t\t\tconst files = Array.isArray(e.detail) ? e.detail : [e.detail];\n\t\t\t\tvalue = files.map((x) => ({ image: x, caption: null }));\n\t\t\t\tgradio.dispatch(\"upload\", value);\n\t\t\t}}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"gallery\" />\n\t\t</BaseFileUpload>\n\t{:else}\n\t\t<Gallery\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{columns}\n\t\t\t{rows}\n\t\t\t{height}\n\t\t\t{preview}\n\t\t\t{object_fit}\n\t\t\t{interactive}\n\t\t\t{allow_preview}\n\t\t\tbind:selected_index\n\t\t\tbind:value\n\t\t\t{show_share_button}\n\t\t\t{show_download_button}\n\t\t\ti18n={gradio.i18n}\n\t\t\t_fetch={gradio.client.fetch}\n\t\t\t{show_fullscreen_button}\n\t\t/>\n\t{/if}\n</Block>\n"], "names": ["createEventDispatcher", "ctx", "dirty", "gallery_changes", "basefileupload_changes", "uploadtext_changes", "block_changes", "loading_status", "$$props", "show_label", "label", "root", "elem_id", "elem_classes", "visible", "value", "container", "scale", "min_width", "columns", "rows", "height", "preview", "allow_preview", "selected_index", "object_fit", "show_share_button", "interactive", "show_download_button", "gradio", "show_fullscreen_button", "dispatch", "clear_status_handler", "e", "files", "$$invalidate", "x", "detail", "change_handler", "no_value"], "mappings": "8uCAWU,CAAA,sBAAAA,WAAqC,oSAuGtC,KAAAC,MAAO,YACLA,EAAM,EAAA,EAAC,OAAO,6rBADhBC,EAAA,CAAA,EAAA,UAAAC,EAAA,KAAAF,MAAO,8BACLA,EAAM,EAAA,EAAC,OAAO,sSA1Cf,0BAGQ,cAAAA,MAAO,yBACV,uBACC,OAAO,EACd,KAAAA,MAAO,YACLA,EAAM,EAAA,EAAC,OAAO,sBACNA,EAAM,EAAA,EAAC,OAAO,0MALfC,EAAA,CAAA,EAAA,UAAAE,EAAA,cAAAH,MAAO,eAGhBC,EAAA,CAAA,EAAA,UAAAE,EAAA,KAAAH,MAAO,8BACLA,EAAM,EAAA,EAAC,OAAO,wCACNA,EAAM,EAAA,EAAC,OAAO,iMAYZ,KAAAA,MAAO,sFAAPC,EAAA,CAAA,EAAA,UAAAG,EAAA,KAAAJ,MAAO,yIA3Bd,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,yIAGd,OAAAA,OAAeA,EAAQ,EAAA,EAAA,iLALf,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,oWAZV,4FAMO,GACD,OAAA,OAAAA,OAAW,SAAWA,EAAM,EAAA,EAAG,4RAA/BC,EAAA,CAAA,EAAA,QAAAI,EAAA,OAAA,OAAAL,OAAW,SAAWA,EAAM,EAAA,EAAG,8KAjDnC,eAAAM,CAA6B,EAAAC,GAC7B,WAAAC,CAAmB,EAAAD,GACnB,MAAAE,CAAa,EAAAF,GACb,KAAAG,CAAY,EAAAH,EACZ,CAAA,QAAAI,EAAU,EAAE,EAAAJ,GACZ,aAAAK,EAAY,EAAA,EAAAL,EACZ,CAAA,QAAAM,EAAU,EAAI,EAAAN,EACd,CAAA,MAAAO,EAA8D,IAAI,EAAAP,EAClE,CAAA,UAAAQ,EAAY,EAAI,EAAAR,EAChB,CAAA,MAAAS,EAAuB,IAAI,EAAAT,EAC3B,CAAA,UAAAU,EAAgC,MAAS,EAAAV,EACzC,CAAA,QAAAW,GAA0C,CAAC,CAAA,EAAAX,EAC3C,CAAA,KAAAY,EAAsC,MAAS,EAAAZ,EAC/C,CAAA,OAAAa,EAA0B,MAAM,EAAAb,GAChC,QAAAc,CAAgB,EAAAd,EAChB,CAAA,cAAAe,EAAgB,EAAI,EAAAf,EACpB,CAAA,eAAAgB,EAAgC,IAAI,EAAAhB,EACpC,CAAA,WAAAiB,EACV,OAAO,EAAAjB,EACG,CAAA,kBAAAkB,EAAoB,EAAK,EAAAlB,GACzB,YAAAmB,CAAoB,EAAAnB,EACpB,CAAA,qBAAAoB,EAAuB,EAAK,EAAApB,GAC5B,OAAAqB,CAQT,EAAArB,EACS,CAAA,uBAAAsB,EAAyB,EAAI,EAAAtB,EAElC,MAAAuB,EAAW/B,KAsBOgC,EAAA,IAAAH,EAAO,SAAS,eAAgBtB,CAAc,IAaxD0B,GAAC,CACN,MAAAC,EAAQ,MAAM,QAAQD,EAAE,MAAM,EAAIA,EAAE,OAAU,CAAAA,EAAE,MAAM,EAC5DE,EAAA,EAAApB,EAAQmB,EAAM,IAAKE,IAAS,CAAA,MAAOA,EAAG,QAAS,IAAI,EAAA,CAAA,EACnDP,EAAO,SAAS,SAAUd,CAAK,OAEnB,OAAAsB,KAAM,CAClBF,EAAA,EAAA5B,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BsB,EAAO,SAAS,QAASQ,CAAM,qDAOf,MAAAC,EAAA,IAAAT,EAAO,SAAS,SAAUd,CAAK,IACpCkB,GAAMJ,EAAO,SAAS,SAAUI,EAAE,MAAM,IACzCA,GAAMJ,EAAO,SAAS,QAASI,EAAE,MAAM,IACvCA,GAAMJ,EAAO,SAAS,QAASI,EAAE,MAAM,m6BAnDjDE,EAAA,GAAAI,EAAW,MAAM,QAAQxB,CAAK,EAAIA,EAAM,SAAW,GAAKA,CAAK,mBAC7CgB,EAAS,eAAiB,eAAAP,CAAc,CAAA"}