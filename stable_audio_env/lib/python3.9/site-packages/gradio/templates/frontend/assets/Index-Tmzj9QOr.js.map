{"version": 3, "file": "Index-Tmzj9QOr.js", "sources": ["../../../../js/row/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Gradio } from \"@gradio/utils\";\n\n\texport let equal_height = true;\n\texport let elem_id: string;\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"default\" | \"panel\" | \"compact\" = \"default\";\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let gradio: Gradio | undefined = undefined;\n\texport let show_progress = false;\n</script>\n\n<div\n\tclass:compact={variant === \"compact\"}\n\tclass:panel={variant === \"panel\"}\n\tclass:unequal-height={equal_height === false}\n\tclass:stretch={equal_height}\n\tclass:hide={!visible}\n\tid={elem_id}\n\tclass={elem_classes.join(\" \")}\n>\n\t{#if loading_status && show_progress && gradio}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tstatus={loading_status\n\t\t\t\t? loading_status.status == \"pending\"\n\t\t\t\t\t? \"generating\"\n\t\t\t\t\t: loading_status.status\n\t\t\t\t: null}\n\t\t/>\n\t{/if}\n\t<slot />\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--layout-gap);\n\t\twidth: var(--size-full);\n\t\tposition: relative;\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\t.compact > :global(*),\n\t.compact :global(.box) {\n\t\tborder-radius: 0;\n\t}\n\t.compact,\n\t.panel {\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--size-2);\n\t}\n\t.unequal-height {\n\t\talign-items: flex-start;\n\t}\n\n\t.stretch {\n\t\talign-items: stretch;\n\t}\n\n\tdiv > :global(*),\n\tdiv > :global(.form > *) {\n\t\tflex: 1 1 0%;\n\t\tflex-wrap: wrap;\n\t\tmin-width: min(160px, 100%);\n\t}\n</style>\n"], "names": ["ctx", "dirty", "create_if_block", "toggle_class", "div", "insert", "target", "anchor", "equal_height", "$$props", "elem_id", "elem_classes", "visible", "variant", "loading_status", "gradio", "show_progress"], "mappings": "oiBA0Be,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,GACV,OAAAA,EAAA,CAAA,EACLA,EAAc,CAAA,EAAC,QAAU,UACxB,aACAA,EAAe,CAAA,EAAA,OAChB,wJAPSC,EAAA,IAAA,CAAA,WAAAD,KAAO,UAAU,EACvBC,EAAA,IAAA,CAAA,KAAAD,KAAO,IAAI,UACbA,EAAc,CAAA,CAAA,SACV,OAAAA,EAAA,CAAA,EACLA,EAAc,CAAA,EAAC,QAAU,UACxB,aACAA,EAAe,CAAA,EAAA,OAChB,iIATAA,EAAc,CAAA,GAAIA,EAAa,CAAA,GAAIA,EAAM,CAAA,GAAAE,EAAAF,CAAA,iGAH1CA,EAAO,CAAA,CAAA,kBACJA,EAAY,CAAA,EAAC,KAAK,GAAG,CAAA,EAAA,gBAAA,EANbG,EAAAC,EAAA,UAAAJ,OAAY,SAAS,EACvBG,EAAAC,EAAA,QAAAJ,OAAY,OAAO,EACVG,EAAAC,EAAA,iBAAAJ,OAAiB,EAAK,gBAC7BA,EAAY,CAAA,CAAA,cACdA,EAAO,CAAA,CAAA,UALrBK,EAsBKC,EAAAF,EAAAG,CAAA,sDAbCP,EAAc,CAAA,GAAIA,EAAa,CAAA,GAAIA,EAAM,CAAA,sLAH1CA,EAAO,CAAA,CAAA,qBACJA,EAAY,CAAA,EAAC,KAAK,GAAG,CAAA,EAAA,+CANbG,EAAAC,EAAA,UAAAJ,OAAY,SAAS,cACvBG,EAAAC,EAAA,QAAAJ,OAAY,OAAO,aACVG,EAAAC,EAAA,iBAAAJ,OAAiB,EAAK,2BAC7BA,EAAY,CAAA,CAAA,0BACdA,EAAO,CAAA,CAAA,wIAfT,CAAA,aAAAQ,EAAe,EAAI,EAAAC,GACnB,QAAAC,CAAe,EAAAD,GACf,aAAAE,EAAY,EAAA,EAAAF,EACZ,CAAA,QAAAG,EAAU,EAAI,EAAAH,EACd,CAAA,QAAAI,EAA2C,SAAS,EAAAJ,EACpD,CAAA,eAAAK,EAA4C,MAAS,EAAAL,EACrD,CAAA,OAAAM,EAA6B,MAAS,EAAAN,EACtC,CAAA,cAAAO,EAAgB,EAAK,EAAAP"}