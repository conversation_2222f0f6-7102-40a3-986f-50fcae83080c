import pe from"./ImagePreview-BpvYwBpC.js";import{I as ke}from"./ImageUploader-Dh2I3hM7.js";import{W as ft}from"./ImageUploader-Dh2I3hM7.js";import{B as Q}from"./Button-BIUaXfcG.js";import{S as R}from"./Index-DB1XLvMK.js";import{E as ve}from"./Empty-BgF7sXBn.js";import{I as Ie}from"./Image-Bsh8Umrh.js";import{U as V}from"./UploadText-CCg0GCB-.js";import{I as ct}from"./Image-CJc3fwmN.js";import{default as gt}from"./Example-Ig2aUhUe.js";import"./Blocks-CyfcXtBq.js";import"./index-BQPjLIsY.js";import"./svelte/svelte.js";import"./BlockLabel-BlSr62f_.js";import"./ShareButton-DRfMJDgB.js";import"./Download-DVtk-Jv3.js";import"./Minimize-X5PPawdt.js";import"./utils-Gtzs_Zla.js";import"./DownloadLink-CHpWw1Ex.js";import"./file-url-SIRImsEF.js";/* empty css                                                   */import"./SelectSource-C3FIO9My.js";import"./Upload-Cp8Go_XF.js";import"./DropdownArrow-B_jYsAai.js";import"./Upload-1-QDTAlg.js";/* empty css                                                   *//* empty css                                              */const{SvelteComponent:Be,add_flush_callback:j,assign:X,bind:q,binding_callbacks:P,check_outros:Y,create_component:p,destroy_component:k,detach:F,empty:Z,flush:h,get_spread_object:$,get_spread_update:y,group_outros:x,init:Se,insert:J,mount_component:v,safe_not_equal:ze,space:ee,transition_in:b,transition_out:w}=window.__gradio__svelte__internal,{afterUpdate:Ue}=window.__gradio__svelte__internal;function Ne(n){let e,s;return e=new Q({props:{visible:n[4],variant:n[0]===null?"dashed":"solid",border_mode:n[24]?"focus":"base",padding:!1,elem_id:n[2],elem_classes:n[3],height:n[9]||void 0,width:n[10],allow_overflow:!1,container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[We]},$$scope:{ctx:n}}}),e.$on("dragenter",n[27]),e.$on("dragleave",n[27]),e.$on("dragover",n[27]),e.$on("drop",n[28]),{c(){p(e.$$.fragment)},m(t,i){v(e,t,i),s=!0},p(t,i){const r={};i[0]&16&&(r.visible=t[4]),i[0]&1&&(r.variant=t[0]===null?"dashed":"solid"),i[0]&16777216&&(r.border_mode=t[24]?"focus":"base"),i[0]&4&&(r.elem_id=t[2]),i[0]&8&&(r.elem_classes=t[3]),i[0]&512&&(r.height=t[9]||void 0),i[0]&1024&&(r.width=t[10]),i[0]&4096&&(r.container=t[12]),i[0]&8192&&(r.scale=t[13]),i[0]&16384&&(r.min_width=t[14]),i[0]&129829219|i[1]&65536&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){k(e,t)}}}function De(n){let e,s;return e=new Q({props:{visible:n[4],variant:"solid",border_mode:n[24]?"focus":"base",padding:!1,elem_id:n[2],elem_classes:n[3],height:n[9]||void 0,width:n[10],allow_overflow:!1,container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[je]},$$scope:{ctx:n}}}),{c(){p(e.$$.fragment)},m(t,i){v(e,t,i),s=!0},p(t,i){const r={};i[0]&16&&(r.visible=t[4]),i[0]&16777216&&(r.border_mode=t[24]?"focus":"base"),i[0]&4&&(r.elem_id=t[2]),i[0]&8&&(r.elem_classes=t[3]),i[0]&512&&(r.height=t[9]||void 0),i[0]&1024&&(r.width=t[10]),i[0]&4096&&(r.container=t[12]),i[0]&8192&&(r.scale=t[13]),i[0]&16384&&(r.min_width=t[14]),i[0]&12617955|i[1]&65536&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){k(e,t)}}}function Ee(n){let e,s;return e=new ve({props:{unpadded_box:!0,size:"large",$$slots:{default:[Je]},$$scope:{ctx:n}}}),{c(){p(e.$$.fragment)},m(t,i){v(e,t,i),s=!0},p(t,i){const r={};i[1]&65536&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){k(e,t)}}}function Pe(n){let e,s;return e=new V({props:{i18n:n[23].i18n,type:"clipboard",mode:"short"}}),{c(){p(e.$$.fragment)},m(t,i){v(e,t,i),s=!0},p(t,i){const r={};i[0]&8388608&&(r.i18n=t[23].i18n),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){k(e,t)}}}function Fe(n){let e,s;return e=new V({props:{i18n:n[23].i18n,type:"image",placeholder:n[21]}}),{c(){p(e.$$.fragment)},m(t,i){v(e,t,i),s=!0},p(t,i){const r={};i[0]&8388608&&(r.i18n=t[23].i18n),i[0]&2097152&&(r.placeholder=t[21]),e.$set(r)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){k(e,t)}}}function Je(n){let e,s;return e=new Ie({}),{c(){p(e.$$.fragment)},m(t,i){v(e,t,i),s=!0},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){k(e,t)}}}function Oe(n){let e,s,t,i;const r=[Fe,Pe,Ee],f=[];function o(a,c){return a[25]==="upload"||!a[25]?0:a[25]==="clipboard"?1:2}return e=o(n),s=f[e]=r[e](n),{c(){s.c(),t=Z()},m(a,c){f[e].m(a,c),J(a,t,c),i=!0},p(a,c){let m=e;e=o(a),e===m?f[e].p(a,c):(x(),w(f[m],1,1,()=>{f[m]=null}),Y(),s=f[e],s?s.p(a,c):(s=f[e]=r[e](a),s.c()),b(s,1),s.m(t.parentNode,t))},i(a){i||(b(s),i=!0)},o(a){w(s),i=!1},d(a){a&&F(t),f[e].d(a)}}}function We(n){let e,s,t,i,r,f,o;const a=[{autoscroll:n[23].autoscroll},{i18n:n[23].i18n},n[1]];let c={};for(let u=0;u<a.length;u+=1)c=X(c,a[u]);e=new R({props:c}),e.$on("clear_status",n[34]);function m(u){n[36](u)}function N(u){n[37](u)}function D(u){n[38](u)}let B={selectable:n[11],root:n[8],sources:n[16],label:n[5],show_label:n[6],pending:n[19],streaming:n[18],mirror_webcam:n[20],max_file_size:n[23].max_file_size,i18n:n[23].i18n,upload:n[23].client.upload,stream_handler:n[23].client.stream,$$slots:{default:[Oe]},$$scope:{ctx:n}};return n[25]!==void 0&&(B.active_source=n[25]),n[0]!==void 0&&(B.value=n[0]),n[24]!==void 0&&(B.dragging=n[24]),t=new ke({props:B}),n[35](t),P.push(()=>q(t,"active_source",m)),P.push(()=>q(t,"value",N)),P.push(()=>q(t,"dragging",D)),t.$on("edit",n[39]),t.$on("clear",n[40]),t.$on("stream",n[41]),t.$on("drag",n[42]),t.$on("upload",n[43]),t.$on("select",n[44]),t.$on("share",n[45]),t.$on("error",n[46]),{c(){p(e.$$.fragment),s=ee(),p(t.$$.fragment)},m(u,_){v(e,u,_),J(u,s,_),v(t,u,_),o=!0},p(u,_){const E=_[0]&8388610?y(a,[_[0]&8388608&&{autoscroll:u[23].autoscroll},_[0]&8388608&&{i18n:u[23].i18n},_[0]&2&&$(u[1])]):{};e.$set(E);const g={};_[0]&2048&&(g.selectable=u[11]),_[0]&256&&(g.root=u[8]),_[0]&65536&&(g.sources=u[16]),_[0]&32&&(g.label=u[5]),_[0]&64&&(g.show_label=u[6]),_[0]&524288&&(g.pending=u[19]),_[0]&262144&&(g.streaming=u[18]),_[0]&1048576&&(g.mirror_webcam=u[20]),_[0]&8388608&&(g.max_file_size=u[23].max_file_size),_[0]&8388608&&(g.i18n=u[23].i18n),_[0]&8388608&&(g.upload=u[23].client.upload),_[0]&8388608&&(g.stream_handler=u[23].client.stream),_[0]&44040192|_[1]&65536&&(g.$$scope={dirty:_,ctx:u}),!i&&_[0]&33554432&&(i=!0,g.active_source=u[25],j(()=>i=!1)),!r&&_[0]&1&&(r=!0,g.value=u[0],j(()=>r=!1)),!f&&_[0]&16777216&&(f=!0,g.dragging=u[24],j(()=>f=!1)),t.$set(g)},i(u){o||(b(e.$$.fragment,u),b(t.$$.fragment,u),o=!0)},o(u){w(e.$$.fragment,u),w(t.$$.fragment,u),o=!1},d(u){u&&F(s),k(e,u),n[35](null),k(t,u)}}}function je(n){let e,s,t,i;const r=[{autoscroll:n[23].autoscroll},{i18n:n[23].i18n},n[1]];let f={};for(let o=0;o<r.length;o+=1)f=X(f,r[o]);return e=new R({props:f}),t=new pe({props:{value:n[0],label:n[5],show_label:n[6],show_download_button:n[7],selectable:n[11],show_share_button:n[15],i18n:n[23].i18n,show_fullscreen_button:n[22]}}),t.$on("select",n[31]),t.$on("share",n[32]),t.$on("error",n[33]),{c(){p(e.$$.fragment),s=ee(),p(t.$$.fragment)},m(o,a){v(e,o,a),J(o,s,a),v(t,o,a),i=!0},p(o,a){const c=a[0]&8388610?y(r,[a[0]&8388608&&{autoscroll:o[23].autoscroll},a[0]&8388608&&{i18n:o[23].i18n},a[0]&2&&$(o[1])]):{};e.$set(c);const m={};a[0]&1&&(m.value=o[0]),a[0]&32&&(m.label=o[5]),a[0]&64&&(m.show_label=o[6]),a[0]&128&&(m.show_download_button=o[7]),a[0]&2048&&(m.selectable=o[11]),a[0]&32768&&(m.show_share_button=o[15]),a[0]&8388608&&(m.i18n=o[23].i18n),a[0]&4194304&&(m.show_fullscreen_button=o[22]),t.$set(m)},i(o){i||(b(e.$$.fragment,o),b(t.$$.fragment,o),i=!0)},o(o){w(e.$$.fragment,o),w(t.$$.fragment,o),i=!1},d(o){o&&F(s),k(e,o),k(t,o)}}}function qe(n){let e,s,t,i;const r=[De,Ne],f=[];function o(a,c){return a[17]?1:0}return e=o(n),s=f[e]=r[e](n),{c(){s.c(),t=Z()},m(a,c){f[e].m(a,c),J(a,t,c),i=!0},p(a,c){let m=e;e=o(a),e===m?f[e].p(a,c):(x(),w(f[m],1,1,()=>{f[m]=null}),Y(),s=f[e],s?s.p(a,c):(s=f[e]=r[e](a),s.c()),b(s,1),s.m(t.parentNode,t))},i(a){i||(b(s),i=!0)},o(a){w(s),i=!1},d(a){a&&F(t),f[e].d(a)}}}function Ce(n,e,s){let{value_is_output:t=!1}=e,{elem_id:i=""}=e,{elem_classes:r=[]}=e,{visible:f=!0}=e,{value:o=null}=e,a=null,{label:c}=e,{show_label:m}=e,{show_download_button:N}=e,{root:D}=e,{height:B}=e,{width:u}=e,{_selectable:_=!1}=e,{container:E=!0}=e,{scale:g=null}=e,{min_width:C=void 0}=e,{loading_status:S}=e,{show_share_button:T=!1}=e,{sources:A=["upload","clipboard","webcam"]}=e,{interactive:O}=e,{streaming:G}=e,{pending:H}=e,{mirror_webcam:K}=e,{placeholder:L=void 0}=e,{show_fullscreen_button:M}=e,{gradio:d}=e;Ue(()=>{s(29,t=!1)});let z,W=null,U;const te=l=>{const I=l;I.preventDefault(),I.stopPropagation(),I.type==="dragenter"||I.type==="dragover"?s(24,z=!0):I.type==="dragleave"&&s(24,z=!1)},se=l=>{if(O){const I=l;I.preventDefault(),I.stopPropagation(),s(24,z=!1),U&&U.loadFilesFromDrop(I)}},ne=({detail:l})=>d.dispatch("select",l),le=({detail:l})=>d.dispatch("share",l),ie=({detail:l})=>d.dispatch("error",l),ae=()=>d.dispatch("clear_status",S);function re(l){P[l?"unshift":"push"](()=>{U=l,s(26,U)})}function oe(l){W=l,s(25,W)}function ue(l){o=l,s(0,o)}function _e(l){z=l,s(24,z)}const fe=()=>d.dispatch("edit"),he=()=>{d.dispatch("clear")},ce=()=>d.dispatch("stream"),me=({detail:l})=>s(24,z=l),ge=()=>d.dispatch("upload"),de=({detail:l})=>d.dispatch("select",l),be=({detail:l})=>d.dispatch("share",l),we=({detail:l})=>{s(1,S=S||{}),s(1,S.status="error",S),d.dispatch("error",l)};return n.$$set=l=>{"value_is_output"in l&&s(29,t=l.value_is_output),"elem_id"in l&&s(2,i=l.elem_id),"elem_classes"in l&&s(3,r=l.elem_classes),"visible"in l&&s(4,f=l.visible),"value"in l&&s(0,o=l.value),"label"in l&&s(5,c=l.label),"show_label"in l&&s(6,m=l.show_label),"show_download_button"in l&&s(7,N=l.show_download_button),"root"in l&&s(8,D=l.root),"height"in l&&s(9,B=l.height),"width"in l&&s(10,u=l.width),"_selectable"in l&&s(11,_=l._selectable),"container"in l&&s(12,E=l.container),"scale"in l&&s(13,g=l.scale),"min_width"in l&&s(14,C=l.min_width),"loading_status"in l&&s(1,S=l.loading_status),"show_share_button"in l&&s(15,T=l.show_share_button),"sources"in l&&s(16,A=l.sources),"interactive"in l&&s(17,O=l.interactive),"streaming"in l&&s(18,G=l.streaming),"pending"in l&&s(19,H=l.pending),"mirror_webcam"in l&&s(20,K=l.mirror_webcam),"placeholder"in l&&s(21,L=l.placeholder),"show_fullscreen_button"in l&&s(22,M=l.show_fullscreen_button),"gradio"in l&&s(23,d=l.gradio)},n.$$.update=()=>{n.$$.dirty[0]&1619001345&&JSON.stringify(o)!==JSON.stringify(a)&&(s(30,a=o),d.dispatch("change"),t||d.dispatch("input"))},[o,S,i,r,f,c,m,N,D,B,u,_,E,g,C,T,A,O,G,H,K,L,M,d,z,W,U,te,se,t,a,ne,le,ie,ae,re,oe,ue,_e,fe,he,ce,me,ge,de,be,we]}class ot extends Be{constructor(e){super(),Se(this,e,Ce,qe,ze,{value_is_output:29,elem_id:2,elem_classes:3,visible:4,value:0,label:5,show_label:6,show_download_button:7,root:8,height:9,width:10,_selectable:11,container:12,scale:13,min_width:14,loading_status:1,show_share_button:15,sources:16,interactive:17,streaming:18,pending:19,mirror_webcam:20,placeholder:21,show_fullscreen_button:22,gradio:23},null,[-1,-1])}get value_is_output(){return this.$$.ctx[29]}set value_is_output(e){this.$$set({value_is_output:e}),h()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),h()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),h()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),h()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),h()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),h()}get show_download_button(){return this.$$.ctx[7]}set show_download_button(e){this.$$set({show_download_button:e}),h()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),h()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),h()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),h()}get _selectable(){return this.$$.ctx[11]}set _selectable(e){this.$$set({_selectable:e}),h()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),h()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),h()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),h()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),h()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),h()}get sources(){return this.$$.ctx[16]}set sources(e){this.$$set({sources:e}),h()}get interactive(){return this.$$.ctx[17]}set interactive(e){this.$$set({interactive:e}),h()}get streaming(){return this.$$.ctx[18]}set streaming(e){this.$$set({streaming:e}),h()}get pending(){return this.$$.ctx[19]}set pending(e){this.$$set({pending:e}),h()}get mirror_webcam(){return this.$$.ctx[20]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),h()}get placeholder(){return this.$$.ctx[21]}set placeholder(e){this.$$set({placeholder:e}),h()}get show_fullscreen_button(){return this.$$.ctx[22]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),h()}get gradio(){return this.$$.ctx[23]}set gradio(e){this.$$set({gradio:e}),h()}}export{gt as BaseExample,ct as BaseImage,ke as BaseImageUploader,pe as BaseStaticImage,ft as Webcam,ot as default};
//# sourceMappingURL=Index-qbjg5-4V.js.map
