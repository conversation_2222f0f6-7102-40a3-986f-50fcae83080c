{"version": 3, "file": "StaticAudio-Dw97rWIw.js", "sources": ["../../../../js/icons/src/Backward.svelte", "../../../../js/icons/src/Forward.svelte", "../../../../js/icons/src/VolumeLow.svelte", "../../../../js/icons/src/VolumeHigh.svelte", "../../../../js/icons/src/VolumeMuted.svelte", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/decoder.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/fetcher.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/event-emitter.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/player.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/draggable.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/renderer.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/timer.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/webaudio.js", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/wavesurfer.js", "../../../../js/audio/shared/audioBufferToWav.ts", "../../../../js/audio/shared/utils.ts", "../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/plugins/regions.js", "../../../../js/audio/shared/VolumeLevels.svelte", "../../../../js/audio/shared/VolumeControl.svelte", "../../../../js/audio/shared/WaveformControls.svelte", "../../../../js/audio/player/AudioPlayer.svelte", "../../../../js/audio/static/StaticAudio.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"24px\"\n\theight=\"24px\"\n\tfill=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z\"\n\t/></svg\n>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"24px\"\n\theight=\"24px\"\n\tfill=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tcolor=\"currentColor\"\n\t><path\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t\td=\"M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z\"\n\t/></svg\n>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tstroke=\"currentColor\"\n\tcolor=\"currentColor\"\n>\n\t<title>Low volume</title>\n\t<path\n\t\td=\"M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/><path\n\t\td=\"M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z\"\n\t\tstroke-width=\"1.5\"\n\t/></svg\n>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tcolor=\"currentColor\"\n>\n\t<title>High volume</title>\n\t<path\n\t\td=\"M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z\"\n\t\tstroke-width=\"1.5\"\n\t/><path\n\t\td=\"M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/><path\n\t\td=\"M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/></svg\n>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tstroke=\"currentColor\"\n\tcolor=\"currentColor\"\n>\n\t<title>Muted volume</title>\n\t<g clip-path=\"url(#clip0_3173_16686)\"\n\t\t><path\n\t\t\td=\"M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14\"\n\t\t\tstroke-width=\"1.5\"\n\t\t\tstroke-linecap=\"round\"\n\t\t\tstroke-linejoin=\"round\"\n\t\t/><path\n\t\t\td=\"M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z\"\n\t\t\tstroke-width=\"1.5\"\n\t\t/></g\n\t><defs\n\t\t><clipPath id=\"clip0_3173_16686\"\n\t\t\t><rect width=\"24\" height=\"24\" fill=\"white\" /></clipPath\n\t\t></defs\n\t></svg\n>\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n/** Decode an array buffer into an audio buffer */\nfunction decode(audioData, sampleRate) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const audioCtx = new AudioContext({ sampleRate });\n        const decode = audioCtx.decodeAudioData(audioData);\n        return decode.finally(() => audioCtx.close());\n    });\n}\n/** Normalize peaks to -1..1 */\nfunction normalize(channelData) {\n    const firstChannel = channelData[0];\n    if (firstChannel.some((n) => n > 1 || n < -1)) {\n        const length = firstChannel.length;\n        let max = 0;\n        for (let i = 0; i < length; i++) {\n            const absN = Math.abs(firstChannel[i]);\n            if (absN > max)\n                max = absN;\n        }\n        for (const channel of channelData) {\n            for (let i = 0; i < length; i++) {\n                channel[i] /= max;\n            }\n        }\n    }\n    return channelData;\n}\n/** Create an audio buffer from pre-decoded audio data */\nfunction createBuffer(channelData, duration) {\n    // If a single array of numbers is passed, make it an array of arrays\n    if (typeof channelData[0] === 'number')\n        channelData = [channelData];\n    // Normalize to -1..1\n    normalize(channelData);\n    return {\n        duration,\n        length: channelData[0].length,\n        sampleRate: channelData[0].length / duration,\n        numberOfChannels: channelData.length,\n        getChannelData: (i) => channelData === null || channelData === void 0 ? void 0 : channelData[i],\n        copyFromChannel: AudioBuffer.prototype.copyFromChannel,\n        copyToChannel: AudioBuffer.prototype.copyToChannel,\n    };\n}\nconst Decoder = {\n    decode,\n    createBuffer,\n};\nexport default Decoder;\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction fetchBlob(url, progressCallback, requestInit) {\n    var _a, _b;\n    return __awaiter(this, void 0, void 0, function* () {\n        // Fetch the resource\n        const response = yield fetch(url, requestInit);\n        // Read the data to track progress\n        {\n            const reader = (_a = response.clone().body) === null || _a === void 0 ? void 0 : _a.getReader();\n            const contentLength = Number((_b = response.headers) === null || _b === void 0 ? void 0 : _b.get('Content-Length'));\n            let receivedLength = 0;\n            // Process the data\n            const processChunk = (done, value) => __awaiter(this, void 0, void 0, function* () {\n                if (done)\n                    return;\n                // Add to the received length\n                receivedLength += (value === null || value === void 0 ? void 0 : value.length) || 0;\n                const percentage = Math.round((receivedLength / contentLength) * 100);\n                progressCallback(percentage);\n                // Continue reading data\n                return reader === null || reader === void 0 ? void 0 : reader.read().then(({ done, value }) => processChunk(done, value));\n            });\n            reader === null || reader === void 0 ? void 0 : reader.read().then(({ done, value }) => processChunk(done, value));\n        }\n        return response.blob();\n    });\n}\nconst Fetcher = {\n    fetchBlob,\n};\nexport default Fetcher;\n", "/** A simple event emitter that can be used to listen to and emit events. */\nclass EventEmitter {\n    constructor() {\n        this.listeners = {};\n        /** Subscribe to an event. Returns an unsubscribe function. */\n        this.on = this.addEventListener;\n        /** Unsubscribe from an event */\n        this.un = this.removeEventListener;\n    }\n    /** Add an event listener */\n    addEventListener(event, listener, options) {\n        if (!this.listeners[event]) {\n            this.listeners[event] = new Set();\n        }\n        this.listeners[event].add(listener);\n        if (options === null || options === void 0 ? void 0 : options.once) {\n            const unsubscribeOnce = () => {\n                this.removeEventListener(event, unsubscribeOnce);\n                this.removeEventListener(event, listener);\n            };\n            this.addEventListener(event, unsubscribeOnce);\n            return unsubscribeOnce;\n        }\n        return () => this.removeEventListener(event, listener);\n    }\n    removeEventListener(event, listener) {\n        var _a;\n        (_a = this.listeners[event]) === null || _a === void 0 ? void 0 : _a.delete(listener);\n    }\n    /** Subscribe to an event only once */\n    once(event, listener) {\n        return this.on(event, listener, { once: true });\n    }\n    /** Clear all events */\n    unAll() {\n        this.listeners = {};\n    }\n    /** Emit an event */\n    emit(eventName, ...args) {\n        if (this.listeners[eventName]) {\n            this.listeners[eventName].forEach((listener) => listener(...args));\n        }\n    }\n}\nexport default EventEmitter;\n", "import EventEmitter from './event-emitter.js';\nclass Player extends EventEmitter {\n    constructor(options) {\n        super();\n        this.isExternalMedia = false;\n        if (options.media) {\n            this.media = options.media;\n            this.isExternalMedia = true;\n        }\n        else {\n            this.media = document.createElement('audio');\n        }\n        // Controls\n        if (options.mediaControls) {\n            this.media.controls = true;\n        }\n        // Autoplay\n        if (options.autoplay) {\n            this.media.autoplay = true;\n        }\n        // Speed\n        if (options.playbackRate != null) {\n            this.onceMediaEvent('canplay', () => {\n                if (options.playbackRate != null) {\n                    this.media.playbackRate = options.playbackRate;\n                }\n            });\n        }\n    }\n    onMediaEvent(event, callback, options) {\n        this.media.addEventListener(event, callback, options);\n        return () => this.media.removeEventListener(event, callback);\n    }\n    onceMediaEvent(event, callback) {\n        return this.onMediaEvent(event, callback, { once: true });\n    }\n    getSrc() {\n        return this.media.currentSrc || this.media.src || '';\n    }\n    revokeSrc() {\n        const src = this.getSrc();\n        if (src.startsWith('blob:')) {\n            URL.revokeObjectURL(src);\n        }\n    }\n    setSrc(url, blob) {\n        const src = this.getSrc();\n        if (src === url)\n            return;\n        this.revokeSrc();\n        const newSrc = blob instanceof Blob ? URL.createObjectURL(blob) : url;\n        this.media.src = newSrc;\n        this.media.load();\n    }\n    destroy() {\n        this.media.pause();\n        if (this.isExternalMedia)\n            return;\n        this.media.remove();\n        this.revokeSrc();\n        this.media.src = '';\n        // Load resets the media element to its initial state\n        this.media.load();\n    }\n    setMediaElement(element) {\n        this.media = element;\n    }\n    /** Start playing the audio */\n    play() {\n        return this.media.play();\n    }\n    /** Pause the audio */\n    pause() {\n        this.media.pause();\n    }\n    /** Check if the audio is playing */\n    isPlaying() {\n        return !this.media.paused && !this.media.ended;\n    }\n    /** Jumpt to a specific time in the audio (in seconds) */\n    setTime(time) {\n        this.media.currentTime = time;\n    }\n    /** Get the duration of the audio in seconds */\n    getDuration() {\n        return this.media.duration;\n    }\n    /** Get the current audio position in seconds */\n    getCurrentTime() {\n        return this.media.currentTime;\n    }\n    /** Get the audio volume */\n    getVolume() {\n        return this.media.volume;\n    }\n    /** Set the audio volume */\n    setVolume(volume) {\n        this.media.volume = volume;\n    }\n    /** Get the audio muted state */\n    getMuted() {\n        return this.media.muted;\n    }\n    /** Mute or unmute the audio */\n    setMuted(muted) {\n        this.media.muted = muted;\n    }\n    /** Get the playback speed */\n    getPlaybackRate() {\n        return this.media.playbackRate;\n    }\n    /** Set the playback speed, pass an optional false to NOT preserve the pitch */\n    setPlaybackRate(rate, preservePitch) {\n        // preservePitch is true by default in most browsers\n        if (preservePitch != null) {\n            this.media.preservesPitch = preservePitch;\n        }\n        this.media.playbackRate = rate;\n    }\n    /** Get the HTML media element */\n    getMediaElement() {\n        return this.media;\n    }\n    /** Set a sink id to change the audio output device */\n    setSinkId(sinkId) {\n        // See https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/setSinkId\n        const media = this.media;\n        return media.setSinkId(sinkId);\n    }\n}\nexport default Player;\n", "export function makeDraggable(element, onDrag, onStart, onEnd, threshold = 5) {\n    let unsub = () => {\n        return;\n    };\n    if (!element)\n        return unsub;\n    const down = (e) => {\n        // Ignore the right mouse button\n        if (e.button === 2)\n            return;\n        e.preventDefault();\n        e.stopPropagation();\n        element.style.touchAction = 'none';\n        let startX = e.clientX;\n        let startY = e.clientY;\n        let isDragging = false;\n        const move = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            const x = e.clientX;\n            const y = e.clientY;\n            if (isDragging || Math.abs(x - startX) >= threshold || Math.abs(y - startY) >= threshold) {\n                const { left, top } = element.getBoundingClientRect();\n                if (!isDragging) {\n                    isDragging = true;\n                    onStart === null || onStart === void 0 ? void 0 : onStart(startX - left, startY - top);\n                }\n                onDrag(x - startX, y - startY, x - left, y - top);\n                startX = x;\n                startY = y;\n            }\n        };\n        const click = (e) => {\n            if (isDragging) {\n                e.preventDefault();\n                e.stopPropagation();\n            }\n        };\n        const up = () => {\n            element.style.touchAction = '';\n            if (isDragging) {\n                onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n            }\n            unsub();\n        };\n        document.addEventListener('pointermove', move);\n        document.addEventListener('pointerup', up);\n        document.addEventListener('pointerleave', up);\n        document.addEventListener('click', click, true);\n        unsub = () => {\n            document.removeEventListener('pointermove', move);\n            document.removeEventListener('pointerup', up);\n            document.removeEventListener('pointerleave', up);\n            setTimeout(() => {\n                document.removeEventListener('click', click, true);\n            }, 10);\n        };\n    };\n    element.addEventListener('pointerdown', down);\n    return () => {\n        unsub();\n        element.removeEventListener('pointerdown', down);\n    };\n}\n", "import { makeDraggable } from './draggable.js';\nimport EventEmitter from './event-emitter.js';\nclass Renderer extends EventEmitter {\n    constructor(options, audioElement) {\n        super();\n        this.timeouts = [];\n        this.isScrolling = false;\n        this.audioData = null;\n        this.resizeObserver = null;\n        this.isDragging = false;\n        this.options = options;\n        const parent = this.parentFromOptionsContainer(options.container);\n        this.parent = parent;\n        const [div, shadow] = this.initHtml();\n        parent.appendChild(div);\n        this.container = div;\n        this.scrollContainer = shadow.querySelector('.scroll');\n        this.wrapper = shadow.querySelector('.wrapper');\n        this.canvasWrapper = shadow.querySelector('.canvases');\n        this.progressWrapper = shadow.querySelector('.progress');\n        this.cursor = shadow.querySelector('.cursor');\n        if (audioElement) {\n            shadow.appendChild(audioElement);\n        }\n        this.initEvents();\n    }\n    parentFromOptionsContainer(container) {\n        let parent;\n        if (typeof container === 'string') {\n            parent = document.querySelector(container);\n        }\n        else if (container instanceof HTMLElement) {\n            parent = container;\n        }\n        if (!parent) {\n            throw new Error('Container not found');\n        }\n        return parent;\n    }\n    initEvents() {\n        const getClickPosition = (e) => {\n            const rect = this.wrapper.getBoundingClientRect();\n            const x = e.clientX - rect.left;\n            const y = e.clientX - rect.left;\n            const relativeX = x / rect.width;\n            const relativeY = y / rect.height;\n            return [relativeX, relativeY];\n        };\n        // Add a click listener\n        this.wrapper.addEventListener('click', (e) => {\n            const [x, y] = getClickPosition(e);\n            this.emit('click', x, y);\n        });\n        // Add a double click listener\n        this.wrapper.addEventListener('dblclick', (e) => {\n            const [x, y] = getClickPosition(e);\n            this.emit('dblclick', x, y);\n        });\n        // Drag\n        if (this.options.dragToSeek) {\n            this.initDrag();\n        }\n        // Add a scroll listener\n        this.scrollContainer.addEventListener('scroll', () => {\n            const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainer;\n            const startX = scrollLeft / scrollWidth;\n            const endX = (scrollLeft + clientWidth) / scrollWidth;\n            this.emit('scroll', startX, endX);\n        });\n        // Re-render the waveform on container resize\n        const delay = this.createDelay(100);\n        this.resizeObserver = new ResizeObserver(() => {\n            delay(() => this.reRender());\n        });\n        this.resizeObserver.observe(this.scrollContainer);\n    }\n    initDrag() {\n        makeDraggable(this.wrapper, \n        // On drag\n        (_, __, x) => {\n            this.emit('drag', Math.max(0, Math.min(1, x / this.wrapper.getBoundingClientRect().width)));\n        }, \n        // On start drag\n        () => (this.isDragging = true), \n        // On end drag\n        () => (this.isDragging = false));\n    }\n    getHeight() {\n        const defaultHeight = 128;\n        if (this.options.height == null)\n            return defaultHeight;\n        if (!isNaN(Number(this.options.height)))\n            return Number(this.options.height);\n        if (this.options.height === 'auto')\n            return this.parent.clientHeight || defaultHeight;\n        return defaultHeight;\n    }\n    initHtml() {\n        const div = document.createElement('div');\n        const shadow = div.attachShadow({ mode: 'open' });\n        shadow.innerHTML = `\n      <style>\n        :host {\n          user-select: none;\n          min-width: 1px;\n        }\n        :host audio {\n          display: block;\n          width: 100%;\n        }\n        :host .scroll {\n          overflow-x: auto;\n          overflow-y: hidden;\n          width: 100%;\n          position: relative;\n        }\n        :host .noScrollbar {\n          scrollbar-color: transparent;\n          scrollbar-width: none;\n        }\n        :host .noScrollbar::-webkit-scrollbar {\n          display: none;\n          -webkit-appearance: none;\n        }\n        :host .wrapper {\n          position: relative;\n          overflow: visible;\n          z-index: 2;\n        }\n        :host .canvases {\n          min-height: ${this.getHeight()}px;\n        }\n        :host .canvases > div {\n          position: relative;\n        }\n        :host canvas {\n          display: block;\n          position: absolute;\n          top: 0;\n          image-rendering: pixelated;\n        }\n        :host .progress {\n          pointer-events: none;\n          position: absolute;\n          z-index: 2;\n          top: 0;\n          left: 0;\n          width: 0;\n          height: 100%;\n          overflow: hidden;\n        }\n        :host .progress > div {\n          position: relative;\n        }\n        :host .cursor {\n          pointer-events: none;\n          position: absolute;\n          z-index: 5;\n          top: 0;\n          left: 0;\n          height: 100%;\n          border-radius: 2px;\n        }\n      </style>\n\n      <div class=\"scroll\" part=\"scroll\">\n        <div class=\"wrapper\" part=\"wrapper\">\n          <div class=\"canvases\"></div>\n          <div class=\"progress\" part=\"progress\"></div>\n          <div class=\"cursor\" part=\"cursor\"></div>\n        </div>\n      </div>\n    `;\n        return [div, shadow];\n    }\n    /** Wavesurfer itself calls this method. Do not call it manually. */\n    setOptions(options) {\n        if (this.options.container !== options.container) {\n            const newParent = this.parentFromOptionsContainer(options.container);\n            newParent.appendChild(this.container);\n            this.parent = newParent;\n        }\n        if (options.dragToSeek && !this.options.dragToSeek) {\n            this.initDrag();\n        }\n        this.options = options;\n        // Re-render the waveform\n        this.reRender();\n    }\n    getWrapper() {\n        return this.wrapper;\n    }\n    getScroll() {\n        return this.scrollContainer.scrollLeft;\n    }\n    destroy() {\n        var _a;\n        this.container.remove();\n        (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n    }\n    createDelay(delayMs = 10) {\n        const context = {};\n        this.timeouts.push(context);\n        return (callback) => {\n            context.timeout && clearTimeout(context.timeout);\n            context.timeout = setTimeout(callback, delayMs);\n        };\n    }\n    // Convert array of color values to linear gradient\n    convertColorValues(color) {\n        if (!Array.isArray(color))\n            return color || '';\n        if (color.length < 2)\n            return color[0] || '';\n        const canvasElement = document.createElement('canvas');\n        const ctx = canvasElement.getContext('2d');\n        const gradient = ctx.createLinearGradient(0, 0, 0, canvasElement.height);\n        const colorStopPercentage = 1 / (color.length - 1);\n        color.forEach((color, index) => {\n            const offset = index * colorStopPercentage;\n            gradient.addColorStop(offset, color);\n        });\n        return gradient;\n    }\n    renderBarWaveform(channelData, options, ctx, vScale) {\n        const topChannel = channelData[0];\n        const bottomChannel = channelData[1] || channelData[0];\n        const length = topChannel.length;\n        const { width, height } = ctx.canvas;\n        const halfHeight = height / 2;\n        const pixelRatio = window.devicePixelRatio || 1;\n        const barWidth = options.barWidth ? options.barWidth * pixelRatio : 1;\n        const barGap = options.barGap ? options.barGap * pixelRatio : options.barWidth ? barWidth / 2 : 0;\n        const barRadius = options.barRadius || 0;\n        const barIndexScale = width / (barWidth + barGap) / length;\n        const rectFn = barRadius && 'roundRect' in ctx ? 'roundRect' : 'rect';\n        ctx.beginPath();\n        let prevX = 0;\n        let maxTop = 0;\n        let maxBottom = 0;\n        for (let i = 0; i <= length; i++) {\n            const x = Math.round(i * barIndexScale);\n            if (x > prevX) {\n                const topBarHeight = Math.round(maxTop * halfHeight * vScale);\n                const bottomBarHeight = Math.round(maxBottom * halfHeight * vScale);\n                const barHeight = topBarHeight + bottomBarHeight || 1;\n                // Vertical alignment\n                let y = halfHeight - topBarHeight;\n                if (options.barAlign === 'top') {\n                    y = 0;\n                }\n                else if (options.barAlign === 'bottom') {\n                    y = height - barHeight;\n                }\n                ctx[rectFn](prevX * (barWidth + barGap), y, barWidth, barHeight, barRadius);\n                prevX = x;\n                maxTop = 0;\n                maxBottom = 0;\n            }\n            const magnitudeTop = Math.abs(topChannel[i] || 0);\n            const magnitudeBottom = Math.abs(bottomChannel[i] || 0);\n            if (magnitudeTop > maxTop)\n                maxTop = magnitudeTop;\n            if (magnitudeBottom > maxBottom)\n                maxBottom = magnitudeBottom;\n        }\n        ctx.fill();\n        ctx.closePath();\n    }\n    renderLineWaveform(channelData, _options, ctx, vScale) {\n        const drawChannel = (index) => {\n            const channel = channelData[index] || channelData[0];\n            const length = channel.length;\n            const { height } = ctx.canvas;\n            const halfHeight = height / 2;\n            const hScale = ctx.canvas.width / length;\n            ctx.moveTo(0, halfHeight);\n            let prevX = 0;\n            let max = 0;\n            for (let i = 0; i <= length; i++) {\n                const x = Math.round(i * hScale);\n                if (x > prevX) {\n                    const h = Math.round(max * halfHeight * vScale) || 1;\n                    const y = halfHeight + h * (index === 0 ? -1 : 1);\n                    ctx.lineTo(prevX, y);\n                    prevX = x;\n                    max = 0;\n                }\n                const value = Math.abs(channel[i] || 0);\n                if (value > max)\n                    max = value;\n            }\n            ctx.lineTo(prevX, halfHeight);\n        };\n        ctx.beginPath();\n        drawChannel(0);\n        drawChannel(1);\n        ctx.fill();\n        ctx.closePath();\n    }\n    renderWaveform(channelData, options, ctx) {\n        ctx.fillStyle = this.convertColorValues(options.waveColor);\n        // Custom rendering function\n        if (options.renderFunction) {\n            options.renderFunction(channelData, ctx);\n            return;\n        }\n        // Vertical scaling\n        let vScale = options.barHeight || 1;\n        if (options.normalize) {\n            const max = Array.from(channelData[0]).reduce((max, value) => Math.max(max, Math.abs(value)), 0);\n            vScale = max ? 1 / max : 1;\n        }\n        // Render waveform as bars\n        if (options.barWidth || options.barGap || options.barAlign) {\n            this.renderBarWaveform(channelData, options, ctx, vScale);\n            return;\n        }\n        // Render waveform as a polyline\n        this.renderLineWaveform(channelData, options, ctx, vScale);\n    }\n    renderSingleCanvas(channelData, options, width, height, start, end, canvasContainer, progressContainer) {\n        const pixelRatio = window.devicePixelRatio || 1;\n        const canvas = document.createElement('canvas');\n        const length = channelData[0].length;\n        canvas.width = Math.round((width * (end - start)) / length);\n        canvas.height = height * pixelRatio;\n        canvas.style.width = `${Math.floor(canvas.width / pixelRatio)}px`;\n        canvas.style.height = `${height}px`;\n        canvas.style.left = `${Math.floor((start * width) / pixelRatio / length)}px`;\n        canvasContainer.appendChild(canvas);\n        const ctx = canvas.getContext('2d');\n        this.renderWaveform(channelData.map((channel) => channel.slice(start, end)), options, ctx);\n        // Draw a progress canvas\n        if (canvas.width > 0 && canvas.height > 0) {\n            const progressCanvas = canvas.cloneNode();\n            const progressCtx = progressCanvas.getContext('2d');\n            progressCtx.drawImage(canvas, 0, 0);\n            // Set the composition method to draw only where the waveform is drawn\n            progressCtx.globalCompositeOperation = 'source-in';\n            progressCtx.fillStyle = this.convertColorValues(options.progressColor);\n            // This rectangle acts as a mask thanks to the composition method\n            progressCtx.fillRect(0, 0, canvas.width, canvas.height);\n            progressContainer.appendChild(progressCanvas);\n        }\n    }\n    renderChannel(channelData, options, width) {\n        // A container for canvases\n        const canvasContainer = document.createElement('div');\n        const height = this.getHeight();\n        canvasContainer.style.height = `${height}px`;\n        this.canvasWrapper.style.minHeight = `${height}px`;\n        this.canvasWrapper.appendChild(canvasContainer);\n        // A container for progress canvases\n        const progressContainer = canvasContainer.cloneNode();\n        this.progressWrapper.appendChild(progressContainer);\n        // Determine the currently visible part of the waveform\n        const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainer;\n        const len = channelData[0].length;\n        const scale = len / scrollWidth;\n        let viewportWidth = Math.min(Renderer.MAX_CANVAS_WIDTH, clientWidth);\n        // Adjust width to avoid gaps between canvases when using bars\n        if (options.barWidth || options.barGap) {\n            const barWidth = options.barWidth || 0.5;\n            const barGap = options.barGap || barWidth / 2;\n            const totalBarWidth = barWidth + barGap;\n            if (viewportWidth % totalBarWidth !== 0) {\n                viewportWidth = Math.floor(viewportWidth / totalBarWidth) * totalBarWidth;\n            }\n        }\n        const start = Math.floor(Math.abs(scrollLeft) * scale);\n        const end = Math.floor(start + viewportWidth * scale);\n        const viewportLen = end - start;\n        // Draw a portion of the waveform from start peak to end peak\n        const draw = (start, end) => {\n            this.renderSingleCanvas(channelData, options, width, height, Math.max(0, start), Math.min(end, len), canvasContainer, progressContainer);\n        };\n        // Draw the waveform in viewport chunks, each with a delay\n        const headDelay = this.createDelay();\n        const tailDelay = this.createDelay();\n        const renderHead = (fromIndex, toIndex) => {\n            draw(fromIndex, toIndex);\n            if (fromIndex > 0) {\n                headDelay(() => {\n                    renderHead(fromIndex - viewportLen, toIndex - viewportLen);\n                });\n            }\n        };\n        const renderTail = (fromIndex, toIndex) => {\n            draw(fromIndex, toIndex);\n            if (toIndex < len) {\n                tailDelay(() => {\n                    renderTail(fromIndex + viewportLen, toIndex + viewportLen);\n                });\n            }\n        };\n        renderHead(start, end);\n        if (end < len) {\n            renderTail(end, end + viewportLen);\n        }\n    }\n    render(audioData) {\n        // Clear previous timeouts\n        this.timeouts.forEach((context) => context.timeout && clearTimeout(context.timeout));\n        this.timeouts = [];\n        // Clear the canvases\n        this.canvasWrapper.innerHTML = '';\n        this.progressWrapper.innerHTML = '';\n        this.wrapper.style.width = '';\n        // Width\n        if (this.options.width != null) {\n            this.scrollContainer.style.width =\n                typeof this.options.width === 'number' ? `${this.options.width}px` : this.options.width;\n        }\n        // Determine the width of the waveform\n        const pixelRatio = window.devicePixelRatio || 1;\n        const parentWidth = this.scrollContainer.clientWidth;\n        const scrollWidth = Math.ceil(audioData.duration * (this.options.minPxPerSec || 0));\n        // Whether the container should scroll\n        this.isScrolling = scrollWidth > parentWidth;\n        const useParentWidth = this.options.fillParent && !this.isScrolling;\n        // Width of the waveform in pixels\n        const width = (useParentWidth ? parentWidth : scrollWidth) * pixelRatio;\n        // Set the width of the wrapper\n        this.wrapper.style.width = useParentWidth ? '100%' : `${scrollWidth}px`;\n        // Set additional styles\n        this.scrollContainer.style.overflowX = this.isScrolling ? 'auto' : 'hidden';\n        this.scrollContainer.classList.toggle('noScrollbar', !!this.options.hideScrollbar);\n        this.cursor.style.backgroundColor = `${this.options.cursorColor || this.options.progressColor}`;\n        this.cursor.style.width = `${this.options.cursorWidth}px`;\n        // Render the waveform\n        if (this.options.splitChannels) {\n            // Render a waveform for each channel\n            for (let i = 0; i < audioData.numberOfChannels; i++) {\n                const options = Object.assign(Object.assign({}, this.options), this.options.splitChannels[i]);\n                this.renderChannel([audioData.getChannelData(i)], options, width);\n            }\n        }\n        else {\n            // Render a single waveform for the first two channels (left and right)\n            const channels = [audioData.getChannelData(0)];\n            if (audioData.numberOfChannels > 1)\n                channels.push(audioData.getChannelData(1));\n            this.renderChannel(channels, this.options, width);\n        }\n        this.audioData = audioData;\n        this.emit('render');\n    }\n    reRender() {\n        // Return if the waveform has not been rendered yet\n        if (!this.audioData)\n            return;\n        // Remember the current cursor position\n        const oldCursorPosition = this.progressWrapper.clientWidth;\n        // Set the new zoom level and re-render the waveform\n        this.render(this.audioData);\n        // Adjust the scroll position so that the cursor stays in the same place\n        const newCursortPosition = this.progressWrapper.clientWidth;\n        this.scrollContainer.scrollLeft += newCursortPosition - oldCursorPosition;\n    }\n    zoom(minPxPerSec) {\n        this.options.minPxPerSec = minPxPerSec;\n        this.reRender();\n    }\n    scrollIntoView(progress, isPlaying = false) {\n        const { clientWidth, scrollLeft, scrollWidth } = this.scrollContainer;\n        const progressWidth = scrollWidth * progress;\n        const center = clientWidth / 2;\n        const minScroll = isPlaying && this.options.autoCenter && !this.isDragging ? center : clientWidth;\n        if (progressWidth > scrollLeft + minScroll || progressWidth < scrollLeft) {\n            // Scroll to the center\n            if (this.options.autoCenter && !this.isDragging) {\n                // If the cursor is in viewport but not centered, scroll to the center slowly\n                const minDiff = center / 20;\n                if (progressWidth - (scrollLeft + center) >= minDiff && progressWidth < scrollLeft + clientWidth) {\n                    this.scrollContainer.scrollLeft += minDiff;\n                }\n                else {\n                    // Otherwise, scroll to the center immediately\n                    this.scrollContainer.scrollLeft = progressWidth - center;\n                }\n            }\n            else if (this.isDragging) {\n                // Scroll just a little bit to allow for some space between the cursor and the edge\n                const gap = 10;\n                this.scrollContainer.scrollLeft =\n                    progressWidth < scrollLeft ? progressWidth - gap : progressWidth - clientWidth + gap;\n            }\n            else {\n                // Scroll to the beginning\n                this.scrollContainer.scrollLeft = progressWidth;\n            }\n        }\n        // Emit the scroll event\n        {\n            const { scrollLeft } = this.scrollContainer;\n            const startX = scrollLeft / scrollWidth;\n            const endX = (scrollLeft + clientWidth) / scrollWidth;\n            this.emit('scroll', startX, endX);\n        }\n    }\n    renderProgress(progress, isPlaying) {\n        if (isNaN(progress))\n            return;\n        const percents = progress * 100;\n        this.canvasWrapper.style.clipPath = `polygon(${percents}% 0, 100% 0, 100% 100%, ${percents}% 100%)`;\n        this.progressWrapper.style.width = `${percents}%`;\n        this.cursor.style.left = `${percents}%`;\n        this.cursor.style.marginLeft = Math.round(percents) === 100 ? `-${this.options.cursorWidth}px` : '';\n        if (this.isScrolling && this.options.autoScroll) {\n            this.scrollIntoView(progress, isPlaying);\n        }\n    }\n}\nRenderer.MAX_CANVAS_WIDTH = 4000;\nexport default Renderer;\n", "import EventEmitter from './event-emitter.js';\nclass Timer extends EventEmitter {\n    constructor() {\n        super(...arguments);\n        this.unsubscribe = () => undefined;\n    }\n    start() {\n        this.unsubscribe = this.on('tick', () => {\n            requestAnimationFrame(() => {\n                this.emit('tick');\n            });\n        });\n        this.emit('tick');\n    }\n    stop() {\n        this.unsubscribe();\n    }\n    destroy() {\n        this.unsubscribe();\n    }\n}\nexport default Timer;\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport EventEmitter from './event-emitter.js';\n/**\n * A Web Audio buffer player emulating the behavior of an HTML5 Audio element.\n */\nclass WebAudioPlayer extends EventEmitter {\n    constructor(audioContext = new AudioContext()) {\n        super();\n        this.bufferNode = null;\n        this.autoplay = false;\n        this.playStartTime = 0;\n        this.playedDuration = 0;\n        this._muted = false;\n        this.buffer = null;\n        this.currentSrc = '';\n        this.paused = true;\n        this.crossOrigin = null;\n        this.audioContext = audioContext;\n        this.gainNode = this.audioContext.createGain();\n        this.gainNode.connect(this.audioContext.destination);\n    }\n    load() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return;\n        });\n    }\n    get src() {\n        return this.currentSrc;\n    }\n    set src(value) {\n        this.currentSrc = value;\n        fetch(value)\n            .then((response) => response.arrayBuffer())\n            .then((arrayBuffer) => this.audioContext.decodeAudioData(arrayBuffer))\n            .then((audioBuffer) => {\n            this.buffer = audioBuffer;\n            this.emit('loadedmetadata');\n            this.emit('canplay');\n            if (this.autoplay)\n                this.play();\n        });\n    }\n    _play() {\n        var _a;\n        if (!this.paused)\n            return;\n        this.paused = false;\n        (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.disconnect();\n        this.bufferNode = this.audioContext.createBufferSource();\n        this.bufferNode.buffer = this.buffer;\n        this.bufferNode.connect(this.gainNode);\n        if (this.playedDuration >= this.duration) {\n            this.playedDuration = 0;\n        }\n        this.bufferNode.start(this.audioContext.currentTime, this.playedDuration);\n        this.playStartTime = this.audioContext.currentTime;\n        this.bufferNode.onended = () => {\n            if (this.currentTime >= this.duration) {\n                this.pause();\n                this.emit('ended');\n            }\n        };\n    }\n    _pause() {\n        var _a;\n        if (this.paused)\n            return;\n        this.paused = true;\n        (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.stop();\n        this.playedDuration += this.audioContext.currentTime - this.playStartTime;\n    }\n    play() {\n        return __awaiter(this, void 0, void 0, function* () {\n            this._play();\n            this.emit('play');\n        });\n    }\n    pause() {\n        this._pause();\n        this.emit('pause');\n    }\n    setSinkId(deviceId) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const ac = this.audioContext;\n            return ac.setSinkId(deviceId);\n        });\n    }\n    get playbackRate() {\n        var _a, _b;\n        return (_b = (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.playbackRate.value) !== null && _b !== void 0 ? _b : 1;\n    }\n    set playbackRate(value) {\n        if (this.bufferNode) {\n            this.bufferNode.playbackRate.value = value;\n        }\n    }\n    get currentTime() {\n        return this.paused ? this.playedDuration : this.playedDuration + this.audioContext.currentTime - this.playStartTime;\n    }\n    set currentTime(value) {\n        this.emit('seeking');\n        if (this.paused) {\n            this.playedDuration = value;\n        }\n        else {\n            this._pause();\n            this.playedDuration = value;\n            this._play();\n        }\n        this.emit('timeupdate');\n    }\n    get duration() {\n        var _a;\n        return ((_a = this.buffer) === null || _a === void 0 ? void 0 : _a.duration) || 0;\n    }\n    get volume() {\n        return this.gainNode.gain.value;\n    }\n    set volume(value) {\n        this.gainNode.gain.value = value;\n        this.emit('volumechange');\n    }\n    get muted() {\n        return this._muted;\n    }\n    set muted(value) {\n        if (this._muted === value)\n            return;\n        this._muted = value;\n        if (this._muted) {\n            this.gainNode.disconnect();\n        }\n        else {\n            this.gainNode.connect(this.audioContext.destination);\n        }\n    }\n    /** Get the GainNode used to play the audio. Can be used to attach filters. */\n    getGainNode() {\n        return this.gainNode;\n    }\n}\nexport default WebAudioPlayer;\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport Decoder from './decoder.js';\nimport Fetcher from './fetcher.js';\nimport Player from './player.js';\nimport Renderer from './renderer.js';\nimport Timer from './timer.js';\nimport WebAudioPlayer from './webaudio.js';\nconst defaultOptions = {\n    waveColor: '#999',\n    progressColor: '#555',\n    cursorWidth: 1,\n    minPxPerSec: 0,\n    fillParent: true,\n    interact: true,\n    dragToSeek: false,\n    autoScroll: true,\n    autoCenter: true,\n    sampleRate: 8000,\n};\nclass WaveSurfer extends Player {\n    /** Create a new WaveSurfer instance */\n    static create(options) {\n        return new WaveSurfer(options);\n    }\n    /** Create a new WaveSurfer instance */\n    constructor(options) {\n        const media = options.media ||\n            (options.backend === 'WebAudio' ? new WebAudioPlayer() : undefined);\n        super({\n            media,\n            mediaControls: options.mediaControls,\n            autoplay: options.autoplay,\n            playbackRate: options.audioRate,\n        });\n        this.plugins = [];\n        this.decodedData = null;\n        this.subscriptions = [];\n        this.mediaSubscriptions = [];\n        this.options = Object.assign({}, defaultOptions, options);\n        this.timer = new Timer();\n        const audioElement = media ? undefined : this.getMediaElement();\n        this.renderer = new Renderer(this.options, audioElement);\n        this.initPlayerEvents();\n        this.initRendererEvents();\n        this.initTimerEvents();\n        this.initPlugins();\n        // Load audio if URL is passed or an external media with an src\n        const url = this.options.url || this.getSrc();\n        if (url) {\n            this.load(url, this.options.peaks, this.options.duration);\n        }\n        else if (this.options.peaks && this.options.duration) {\n            // If pre-decoded peaks and duration are provided, render a waveform w/o loading audio\n            this.loadPredecoded();\n        }\n    }\n    initTimerEvents() {\n        // The timer fires every 16ms for a smooth progress animation\n        this.subscriptions.push(this.timer.on('tick', () => {\n            const currentTime = this.getCurrentTime();\n            this.renderer.renderProgress(currentTime / this.getDuration(), true);\n            this.emit('timeupdate', currentTime);\n            this.emit('audioprocess', currentTime);\n        }));\n    }\n    initPlayerEvents() {\n        this.mediaSubscriptions.push(this.onMediaEvent('timeupdate', () => {\n            const currentTime = this.getCurrentTime();\n            this.renderer.renderProgress(currentTime / this.getDuration(), this.isPlaying());\n            this.emit('timeupdate', currentTime);\n        }), this.onMediaEvent('play', () => {\n            this.emit('play');\n            this.timer.start();\n        }), this.onMediaEvent('pause', () => {\n            this.emit('pause');\n            this.timer.stop();\n        }), this.onMediaEvent('emptied', () => {\n            this.timer.stop();\n        }), this.onMediaEvent('ended', () => {\n            this.emit('finish');\n        }), this.onMediaEvent('seeking', () => {\n            this.emit('seeking', this.getCurrentTime());\n        }));\n    }\n    initRendererEvents() {\n        this.subscriptions.push(\n        // Seek on click\n        this.renderer.on('click', (relativeX, relativeY) => {\n            if (this.options.interact) {\n                this.seekTo(relativeX);\n                this.emit('interaction', relativeX * this.getDuration());\n                this.emit('click', relativeX, relativeY);\n            }\n        }), \n        // Double click\n        this.renderer.on('dblclick', (relativeX, relativeY) => {\n            this.emit('dblclick', relativeX, relativeY);\n        }), \n        // Scroll\n        this.renderer.on('scroll', (startX, endX) => {\n            const duration = this.getDuration();\n            this.emit('scroll', startX * duration, endX * duration);\n        }), \n        // Redraw\n        this.renderer.on('render', () => {\n            this.emit('redraw');\n        }));\n        // Drag\n        {\n            let debounce;\n            this.subscriptions.push(this.renderer.on('drag', (relativeX) => {\n                if (!this.options.interact)\n                    return;\n                // Update the visual position\n                this.renderer.renderProgress(relativeX);\n                // Set the audio position with a debounce\n                clearTimeout(debounce);\n                debounce = setTimeout(() => {\n                    this.seekTo(relativeX);\n                }, this.isPlaying() ? 0 : 200);\n                this.emit('interaction', relativeX * this.getDuration());\n                this.emit('drag', relativeX);\n            }));\n        }\n    }\n    initPlugins() {\n        var _a;\n        if (!((_a = this.options.plugins) === null || _a === void 0 ? void 0 : _a.length))\n            return;\n        this.options.plugins.forEach((plugin) => {\n            this.registerPlugin(plugin);\n        });\n    }\n    unsubscribePlayerEvents() {\n        this.mediaSubscriptions.forEach((unsubscribe) => unsubscribe());\n        this.mediaSubscriptions = [];\n    }\n    /** Set new wavesurfer options and re-render it */\n    setOptions(options) {\n        this.options = Object.assign({}, this.options, options);\n        this.renderer.setOptions(this.options);\n        if (options.audioRate) {\n            this.setPlaybackRate(options.audioRate);\n        }\n        if (options.mediaControls != null) {\n            this.getMediaElement().controls = options.mediaControls;\n        }\n    }\n    /** Register a wavesurfer.js plugin */\n    registerPlugin(plugin) {\n        plugin.init(this);\n        this.plugins.push(plugin);\n        // Unregister plugin on destroy\n        this.subscriptions.push(plugin.once('destroy', () => {\n            this.plugins = this.plugins.filter((p) => p !== plugin);\n        }));\n        return plugin;\n    }\n    /** For plugins only: get the waveform wrapper div */\n    getWrapper() {\n        return this.renderer.getWrapper();\n    }\n    /** Get the current scroll position in pixels */\n    getScroll() {\n        return this.renderer.getScroll();\n    }\n    /** Get all registered plugins */\n    getActivePlugins() {\n        return this.plugins;\n    }\n    loadPredecoded() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.options.peaks && this.options.duration) {\n                this.decodedData = Decoder.createBuffer(this.options.peaks, this.options.duration);\n                yield Promise.resolve(); // wait for event listeners to subscribe\n                this.renderDecoded();\n            }\n        });\n    }\n    renderDecoded() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.decodedData) {\n                this.emit('decode', this.getDuration());\n                this.renderer.render(this.decodedData);\n            }\n        });\n    }\n    loadAudio(url, blob, channelData, duration) {\n        return __awaiter(this, void 0, void 0, function* () {\n            this.emit('load', url);\n            if (!this.options.media && this.isPlaying())\n                this.pause();\n            this.decodedData = null;\n            // Fetch the entire audio as a blob if pre-decoded data is not provided\n            if (!blob && !channelData) {\n                const onProgress = (percentage) => this.emit('loading', percentage);\n                blob = yield Fetcher.fetchBlob(url, onProgress, this.options.fetchParams);\n            }\n            // Set the mediaelement source\n            this.setSrc(url, blob);\n            // Wait for the audio duration\n            // It should be a promise to allow event listeners to subscribe to the ready and decode events\n            duration =\n                (yield Promise.resolve(duration || this.getDuration())) ||\n                    (yield new Promise((resolve) => {\n                        this.onceMediaEvent('loadedmetadata', () => resolve(this.getDuration()));\n                    })) ||\n                    (yield Promise.resolve(0));\n            // Decode the audio data or use user-provided peaks\n            if (channelData) {\n                this.decodedData = Decoder.createBuffer(channelData, duration);\n            }\n            else if (blob) {\n                const arrayBuffer = yield blob.arrayBuffer();\n                this.decodedData = yield Decoder.decode(arrayBuffer, this.options.sampleRate);\n            }\n            this.renderDecoded();\n            this.emit('ready', this.getDuration());\n        });\n    }\n    /** Load an audio file by URL, with optional pre-decoded audio data */\n    load(url, channelData, duration) {\n        return __awaiter(this, void 0, void 0, function* () {\n            yield this.loadAudio(url, undefined, channelData, duration);\n        });\n    }\n    /** Load an audio blob */\n    loadBlob(blob, channelData, duration) {\n        return __awaiter(this, void 0, void 0, function* () {\n            yield this.loadAudio('blob', blob, channelData, duration);\n        });\n    }\n    /** Zoom the waveform by a given pixels-per-second factor */\n    zoom(minPxPerSec) {\n        if (!this.decodedData) {\n            throw new Error('No audio loaded');\n        }\n        this.renderer.zoom(minPxPerSec);\n        this.emit('zoom', minPxPerSec);\n    }\n    /** Get the decoded audio data */\n    getDecodedData() {\n        return this.decodedData;\n    }\n    /** Get decoded peaks */\n    exportPeaks({ channels = 2, maxLength = 8000, precision = 10000 } = {}) {\n        if (!this.decodedData) {\n            throw new Error('The audio has not been decoded yet');\n        }\n        const maxChannels = Math.min(channels, this.decodedData.numberOfChannels);\n        const peaks = [];\n        for (let i = 0; i < maxChannels; i++) {\n            const channel = this.decodedData.getChannelData(i);\n            const data = [];\n            const sampleSize = Math.round(channel.length / maxLength);\n            for (let i = 0; i < maxLength; i++) {\n                const sample = channel.slice(i * sampleSize, (i + 1) * sampleSize);\n                const max = Math.max(...sample);\n                data.push(Math.round(max * precision) / precision);\n            }\n            peaks.push(data);\n        }\n        return peaks;\n    }\n    /** Get the duration of the audio in seconds */\n    getDuration() {\n        let duration = super.getDuration() || 0;\n        // Fall back to the decoded data duration if the media duration is incorrect\n        if ((duration === 0 || duration === Infinity) && this.decodedData) {\n            duration = this.decodedData.duration;\n        }\n        return duration;\n    }\n    /** Toggle if the waveform should react to clicks */\n    toggleInteraction(isInteractive) {\n        this.options.interact = isInteractive;\n    }\n    /** Seek to a percentage of audio as [0..1] (0 = beginning, 1 = end) */\n    seekTo(progress) {\n        const time = this.getDuration() * progress;\n        this.setTime(time);\n    }\n    /** Play or pause the audio */\n    playPause() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.isPlaying() ? this.pause() : this.play();\n        });\n    }\n    /** Stop the audio and go to the beginning */\n    stop() {\n        this.pause();\n        this.setTime(0);\n    }\n    /** Skip N or -N seconds from the current position */\n    skip(seconds) {\n        this.setTime(this.getCurrentTime() + seconds);\n    }\n    /** Empty the waveform by loading a tiny silent audio */\n    empty() {\n        this.load('', [[0]], 0.001);\n    }\n    /** Set HTML media element */\n    setMediaElement(element) {\n        this.unsubscribePlayerEvents();\n        super.setMediaElement(element);\n        this.initPlayerEvents();\n    }\n    /** Unmount wavesurfer */\n    destroy() {\n        this.emit('destroy');\n        this.plugins.forEach((plugin) => plugin.destroy());\n        this.subscriptions.forEach((unsubscribe) => unsubscribe());\n        this.unsubscribePlayerEvents();\n        this.timer.destroy();\n        this.renderer.destroy();\n        super.destroy();\n    }\n}\nexport default WaveSurfer;\n", "export function audioBufferToWav(audioBuffer: AudioBuffer): Uint8Array {\n\tconst numOfChan = audioBuffer.numberOfChannels;\n\tconst length = audioBuffer.length * numOfChan * 2 + 44;\n\tconst buffer = new ArrayBuffer(length);\n\tconst view = new DataView(buffer);\n\tlet offset = 0;\n\n\t// Write WAV header\n\tconst writeString = function (\n\t\tview: DataView,\n\t\toffset: number,\n\t\tstring: string\n\t): void {\n\t\tfor (let i = 0; i < string.length; i++) {\n\t\t\tview.setUint8(offset + i, string.charCodeAt(i));\n\t\t}\n\t};\n\n\twriteString(view, offset, \"RIFF\");\n\toffset += 4;\n\tview.setUint32(offset, length - 8, true);\n\toffset += 4;\n\twriteString(view, offset, \"WAVE\");\n\toffset += 4;\n\twriteString(view, offset, \"fmt \");\n\toffset += 4;\n\tview.setUint32(offset, 16, true);\n\toffset += 4; // Sub-chunk size, 16 for PCM\n\tview.setUint16(offset, 1, true);\n\toffset += 2; // PCM format\n\tview.setUint16(offset, numOfChan, true);\n\toffset += 2;\n\tview.setUint32(offset, audioBuffer.sampleRate, true);\n\toffset += 4;\n\tview.setUint32(offset, audioBuffer.sampleRate * 2 * numOfChan, true);\n\toffset += 4;\n\tview.setUint16(offset, numOfChan * 2, true);\n\toffset += 2;\n\tview.setUint16(offset, 16, true);\n\toffset += 2;\n\twriteString(view, offset, \"data\");\n\toffset += 4;\n\tview.setUint32(offset, audioBuffer.length * numOfChan * 2, true);\n\toffset += 4;\n\n\t// Write PCM audio data\n\tfor (let i = 0; i < audioBuffer.length; i++) {\n\t\tfor (let channel = 0; channel < numOfChan; channel++) {\n\t\t\tconst sample = Math.max(\n\t\t\t\t-1,\n\t\t\t\tMath.min(1, audioBuffer.getChannelData(channel)[i])\n\t\t\t);\n\t\t\tview.setInt16(offset, sample * 0x7fff, true);\n\t\t\toffset += 2;\n\t\t}\n\t}\n\n\treturn new Uint8Array(buffer);\n}\n", "import type WaveSurfer from \"wavesurfer.js\";\nimport { audioBufferToWav } from \"./audioBufferToWav\";\n\nexport interface LoadedParams {\n\tautoplay?: boolean;\n}\n\nexport function blob_to_data_url(blob: Blob): Promise<string> {\n\treturn new Promise((fulfill, reject) => {\n\t\tlet reader = new FileReader();\n\t\treader.onerror = reject;\n\t\treader.onload = () => fulfill(reader.result as string);\n\t\treader.readAsDataURL(blob);\n\t});\n}\n\nexport const process_audio = async (\n\taudioBuffer: AudioBuffer,\n\tstart?: number,\n\tend?: number,\n\twaveform_sample_rate?: number\n): Promise<Uint8Array> => {\n\tconst audioContext = new AudioContext({\n\t\tsampleRate: waveform_sample_rate || audioBuffer.sampleRate\n\t});\n\tconst numberOfChannels = audioBuffer.numberOfChannels;\n\tconst sampleRate = waveform_sample_rate || audioBuffer.sampleRate;\n\n\tlet trimmedLength = audioBuffer.length;\n\tlet startOffset = 0;\n\n\tif (start && end) {\n\t\tstartOffset = Math.round(start * sampleRate);\n\t\tconst endOffset = Math.round(end * sampleRate);\n\t\ttrimmedLength = endOffset - startOffset;\n\t}\n\n\tconst trimmedAudioBuffer = audioContext.createBuffer(\n\t\tnumberOfChannels,\n\t\ttrimmedLength,\n\t\tsampleRate\n\t);\n\n\tfor (let channel = 0; channel < numberOfChannels; channel++) {\n\t\tconst channelData = audioBuffer.getChannelData(channel);\n\t\tconst trimmedData = trimmedAudioBuffer.getChannelData(channel);\n\t\tfor (let i = 0; i < trimmedLength; i++) {\n\t\t\ttrimmedData[i] = channelData[startOffset + i];\n\t\t}\n\t}\n\n\treturn audioBufferToWav(trimmedAudioBuffer);\n};\n\nexport function loaded(\n\tnode: HTMLAudioElement,\n\t{ autoplay }: LoadedParams = {}\n): void {\n\tasync function handle_playback(): Promise<void> {\n\t\tif (!autoplay) return;\n\t\tnode.pause();\n\t\tawait node.play();\n\t}\n}\n\nexport const skip_audio = (waveform: WaveSurfer, amount: number): void => {\n\tif (!waveform) return;\n\twaveform.skip(amount);\n};\n\nexport const get_skip_rewind_amount = (\n\taudio_duration: number,\n\tskip_length?: number | null\n): number => {\n\tif (!skip_length) {\n\t\tskip_length = 5;\n\t}\n\treturn (audio_duration / 100) * skip_length || 5;\n};\n", "class t{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==i?void 0:i.once){const i=()=>{this.removeEventListener(t,i),this.removeEventListener(t,e)};return this.addEventListener(t,i),i}return()=>this.removeEventListener(t,e)}removeEventListener(t,e){var i;null===(i=this.listeners[t])||void 0===i||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}class e extends t{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t()))}}function i(t,e,i,n,s=5){let r=()=>{};if(!t)return r;const o=o=>{if(2===o.button)return;o.preventDefault(),o.stopPropagation(),t.style.touchAction=\"none\";let a=o.clientX,h=o.clientY,l=!1;const d=n=>{n.preventDefault(),n.stopPropagation();const r=n.clientX,o=n.clientY;if(l||Math.abs(r-a)>=s||Math.abs(o-h)>=s){const{left:n,top:s}=t.getBoundingClientRect();l||(l=!0,null==i||i(a-n,h-s)),e(r-a,o-h,r-n,o-s),a=r,h=o}},u=t=>{l&&(t.preventDefault(),t.stopPropagation())},c=()=>{t.style.touchAction=\"\",l&&(null==n||n()),r()};document.addEventListener(\"pointermove\",d),document.addEventListener(\"pointerup\",c),document.addEventListener(\"pointerleave\",c),document.addEventListener(\"click\",u,!0),r=()=>{document.removeEventListener(\"pointermove\",d),document.removeEventListener(\"pointerup\",c),document.removeEventListener(\"pointerleave\",c),setTimeout((()=>{document.removeEventListener(\"click\",u,!0)}),10)}};return t.addEventListener(\"pointerdown\",o),()=>{r(),t.removeEventListener(\"pointerdown\",o)}}class n extends t{constructor(t,e,i=0){var n,s,r,o,a,h,l;super(),this.totalDuration=e,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.id=t.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(t.start),this.end=this.clampPosition(null!==(n=t.end)&&void 0!==n?n:t.start),this.drag=null===(s=t.drag)||void 0===s||s,this.resize=null===(r=t.resize)||void 0===r||r,this.color=null!==(o=t.color)&&void 0!==o?o:\"rgba(0, 0, 0, 0.1)\",this.minLength=null!==(a=t.minLength)&&void 0!==a?a:this.minLength,this.maxLength=null!==(h=t.maxLength)&&void 0!==h?h:this.maxLength,this.channelIdx=null!==(l=t.channelIdx)&&void 0!==l?l:-1,this.element=this.initElement(),this.setContent(t.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(t){return Math.max(0,Math.min(this.totalDuration,t))}setPart(){const t=this.start===this.end;this.element.setAttribute(\"part\",`${t?\"marker\":\"region\"} ${this.id}`)}addResizeHandles(t){const e=document.createElement(\"div\");e.setAttribute(\"data-resize\",\"left\"),e.setAttribute(\"style\",\"\\n        position: absolute;\\n        z-index: 2;\\n        width: 6px;\\n        height: 100%;\\n        top: 0;\\n        left: 0;\\n        border-left: 2px solid rgba(0, 0, 0, 0.5);\\n        border-radius: 2px 0 0 2px;\\n        cursor: ew-resize;\\n        word-break: keep-all;\\n      \"),e.setAttribute(\"part\",\"region-handle region-handle-left\");const n=e.cloneNode();n.setAttribute(\"data-resize\",\"right\"),n.style.left=\"\",n.style.right=\"0\",n.style.borderRight=n.style.borderLeft,n.style.borderLeft=\"\",n.style.borderRadius=\"0 2px 2px 0\",n.setAttribute(\"part\",\"region-handle region-handle-right\"),t.appendChild(e),t.appendChild(n);i(e,(t=>this.onResize(t,\"start\")),(()=>null),(()=>this.onEndResizing()),1),i(n,(t=>this.onResize(t,\"end\")),(()=>null),(()=>this.onEndResizing()),1)}removeResizeHandles(t){const e=t.querySelector('[data-resize=\"left\"]'),i=t.querySelector('[data-resize=\"right\"]');e&&t.removeChild(e),i&&t.removeChild(i)}initElement(){const t=document.createElement(\"div\"),e=this.start===this.end;let i=0,n=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(n=100/this.numberOfChannels,i=n*this.channelIdx),t.setAttribute(\"style\",`\\n      position: absolute;\\n      top: ${i}%;\\n      height: ${n}%;\\n      background-color: ${e?\"none\":this.color};\\n      border-left: ${e?\"2px solid \"+this.color:\"none\"};\\n      border-radius: 2px;\\n      box-sizing: border-box;\\n      transition: background-color 0.2s ease;\\n      cursor: ${this.drag?\"grab\":\"default\"};\\n      pointer-events: all;\\n    `),!e&&this.resize&&this.addResizeHandles(t),t}renderPosition(){const t=this.start/this.totalDuration,e=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*t+\"%\",this.element.style.right=100*e+\"%\"}initMouseEvents(){const{element:t}=this;t&&(t.addEventListener(\"click\",(t=>this.emit(\"click\",t))),t.addEventListener(\"mouseenter\",(t=>this.emit(\"over\",t))),t.addEventListener(\"mouseleave\",(t=>this.emit(\"leave\",t))),t.addEventListener(\"dblclick\",(t=>this.emit(\"dblclick\",t))),i(t,(t=>this.onMove(t)),(()=>this.onStartMoving()),(()=>this.onEndMoving())))}onStartMoving(){this.drag&&(this.element.style.cursor=\"grabbing\")}onEndMoving(){this.drag&&(this.element.style.cursor=\"grab\",this.emit(\"update-end\"))}_onUpdate(t,e){if(!this.element.parentElement)return;const i=t/this.element.parentElement.clientWidth*this.totalDuration,n=e&&\"start\"!==e?this.start:this.start+i,s=e&&\"end\"!==e?this.end:this.end+i,r=s-n;n>=0&&s<=this.totalDuration&&n<=s&&r>=this.minLength&&r<=this.maxLength&&(this.start=n,this.end=s,this.renderPosition(),this.emit(\"update\"))}onMove(t){this.drag&&this._onUpdate(t)}onResize(t,e){this.resize&&this._onUpdate(t,e)}onEndResizing(){this.resize&&this.emit(\"update-end\")}_setTotalDuration(t){this.totalDuration=t,this.renderPosition()}play(){this.emit(\"play\")}setContent(t){var e;if(null===(e=this.content)||void 0===e||e.remove(),t){if(\"string\"==typeof t){this.content=document.createElement(\"div\");const e=this.start===this.end;this.content.style.padding=`0.2em ${e?.2:.4}em`,this.content.textContent=t}else this.content=t;this.content.setAttribute(\"part\",\"region-content\"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(t){var e,i;if(t.color&&(this.color=t.color,this.element.style.backgroundColor=this.color),void 0!==t.drag&&(this.drag=t.drag,this.element.style.cursor=this.drag?\"grab\":\"default\"),void 0!==t.start||void 0!==t.end){const n=this.start===this.end;this.start=this.clampPosition(null!==(e=t.start)&&void 0!==e?e:this.start),this.end=this.clampPosition(null!==(i=t.end)&&void 0!==i?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(t.content&&this.setContent(t.content),t.id&&(this.id=t.id,this.setPart()),void 0!==t.resize&&t.resize!==this.resize){const e=this.start===this.end;this.resize=t.resize,this.resize&&!e?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit(\"remove\"),this.element.remove(),this.element=null}}class s extends e{constructor(t){super(t),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(t){return new s(t)}onInit(){if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let t=[];this.subscriptions.push(this.wavesurfer.on(\"timeupdate\",(e=>{const i=this.regions.filter((t=>t.start<=e&&t.end>=e));i.forEach((e=>{t.includes(e)||this.emit(\"region-in\",e)})),t.forEach((t=>{i.includes(t)||this.emit(\"region-out\",t)})),t=i})))}initRegionsContainer(){const t=document.createElement(\"div\");return t.setAttribute(\"style\",\"\\n      position: absolute;\\n      top: 0;\\n      left: 0;\\n      width: 100%;\\n      height: 100%;\\n      z-index: 3;\\n      pointer-events: none;\\n    \"),t}getRegions(){return this.regions}avoidOverlapping(t){if(!t.content)return;const e=t.content,i=e.getBoundingClientRect().left,n=t.element.scrollWidth,s=this.regions.filter((e=>{if(e===t||!e.content)return!1;const s=e.content.getBoundingClientRect().left,r=e.element.scrollWidth;return i<s+r&&s<i+n})).map((t=>{var e;return(null===(e=t.content)||void 0===e?void 0:e.getBoundingClientRect().height)||0})).reduce(((t,e)=>t+e),0);e.style.marginTop=`${s}px`}saveRegion(t){this.regionsContainer.appendChild(t.element),this.avoidOverlapping(t),this.regions.push(t);const e=[t.on(\"update-end\",(()=>{this.avoidOverlapping(t),this.emit(\"region-updated\",t)})),t.on(\"play\",(()=>{var e,i;null===(e=this.wavesurfer)||void 0===e||e.play(),null===(i=this.wavesurfer)||void 0===i||i.setTime(t.start)})),t.on(\"click\",(e=>{this.emit(\"region-clicked\",t,e)})),t.on(\"dblclick\",(e=>{this.emit(\"region-double-clicked\",t,e)})),t.once(\"remove\",(()=>{e.forEach((t=>t())),this.regions=this.regions.filter((e=>e!==t))}))];this.subscriptions.push(...e),this.emit(\"region-created\",t)}addRegion(t){var e,i;if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");const s=this.wavesurfer.getDuration(),r=null===(i=null===(e=this.wavesurfer)||void 0===e?void 0:e.getDecodedData())||void 0===i?void 0:i.numberOfChannels,o=new n(t,s,r);return s?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once(\"ready\",(t=>{o._setTotalDuration(t),this.saveRegion(o)}))),o}enableDragSelection(t){var e,s;const r=null===(s=null===(e=this.wavesurfer)||void 0===e?void 0:e.getWrapper())||void 0===s?void 0:s.querySelector(\"div\");if(!r)return()=>{};let o=null,a=0;return i(r,((t,e,i)=>{o&&o._onUpdate(t,i>a?\"end\":\"start\")}),(e=>{var i,s;if(a=e,!this.wavesurfer)return;const r=this.wavesurfer.getDuration(),h=null===(s=null===(i=this.wavesurfer)||void 0===i?void 0:i.getDecodedData())||void 0===s?void 0:s.numberOfChannels,l=this.wavesurfer.getWrapper().clientWidth,d=e/l*r,u=(e+5)/l*r;o=new n(Object.assign(Object.assign({},t),{start:d,end:u}),r,h),this.regionsContainer.appendChild(o.element)}),(()=>{o&&(this.saveRegion(o),o=null)}))}clearRegions(){this.regions.forEach((t=>t.remove()))}destroy(){this.clearRegions(),super.destroy()}}export{s as default};\n", "<script lang=\"ts\">\n\timport { VolumeMuted, VolumeHigh, VolumeLow } from \"@gradio/icons\";\n\texport let currentVolume: number;\n</script>\n\n{#if currentVolume == 0}\n\t<VolumeMuted />\n{:else if currentVolume < 0.5}\n\t<VolumeLow />\n{:else if currentVolume >= 0.5}\n\t<VolumeHigh />\n{/if}\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\n\texport let currentVolume = 1;\n\texport let show_volume_slider = false;\n\texport let waveform: WaveSurfer | undefined;\n\n\tlet volumeElement: HTMLInputElement;\n\n\tonMount(() => {\n\t\tadjustSlider();\n\t});\n\n\tconst adjustSlider = (): void => {\n\t\tlet slider = volumeElement;\n\t\tif (!slider) return;\n\n\t\tslider.style.background = `linear-gradient(to right, var(--color-accent) ${\n\t\t\tcurrentVolume * 100\n\t\t}%, var(--neutral-400) ${currentVolume * 100}%)`;\n\t};\n\n\t$: currentVolume, adjustSlider();\n</script>\n\n<input\n\tbind:this={volumeElement}\n\tid=\"volume\"\n\tclass=\"volume-slider\"\n\ttype=\"range\"\n\tmin=\"0\"\n\tmax=\"1\"\n\tstep=\"0.01\"\n\tvalue={currentVolume}\n\ton:focusout={() => (show_volume_slider = false)}\n\ton:input={(e) => {\n\t\tif (e.target instanceof HTMLInputElement) {\n\t\t\tcurrentVolume = parseFloat(e.target.value);\n\t\t\twaveform?.setVolume(currentVolume);\n\t\t}\n\t}}\n/>\n\n<style>\n\t.volume-slider {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\twidth: var(--size-20);\n\t\taccent-color: var(--color-accent);\n\t\theight: 4px;\n\t\tcursor: pointer;\n\t\toutline: none;\n\t\tborder-radius: 15px;\n\t\tbackground-color: var(--neutral-400);\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-thumb {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\theight: 15px;\n\t\twidth: 15px;\n\t\tbackground-color: var(--color-accent);\n\t\tborder-radius: 50%;\n\t\tborder: none;\n\t\ttransition: 0.2s ease-in-out;\n\t}\n\n\tinput[type=\"range\"]::-moz-range-thumb {\n\t\theight: 15px;\n\t\twidth: 15px;\n\t\tbackground-color: var(--color-accent);\n\t\tborder-radius: 50%;\n\t\tborder: none;\n\t\ttransition: 0.2s ease-in-out;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Play, Pause, Forward, Backward, Undo, Trim } from \"@gradio/icons\";\n\timport { get_skip_rewind_amount } from \"../shared/utils\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport RegionsPlugin, {\n\t\ttype Region\n\t} from \"wavesurfer.js/dist/plugins/regions.js\";\n\timport type { WaveformOptions } from \"./types\";\n\timport VolumeLevels from \"./VolumeLevels.svelte\";\n\timport VolumeControl from \"./VolumeControl.svelte\";\n\n\texport let waveform: WaveSurfer | undefined;\n\texport let audio_duration: number;\n\texport let i18n: I18nFormatter;\n\texport let playing: boolean;\n\texport let show_redo = false;\n\texport let interactive = false;\n\texport let handle_trim_audio: (start: number, end: number) => void;\n\texport let mode = \"\";\n\texport let container: HTMLDivElement;\n\texport let handle_reset_value: () => void;\n\texport let waveform_options: WaveformOptions = {};\n\texport let trim_region_settings: WaveformOptions = {};\n\texport let show_volume_slider = false;\n\texport let editable = true;\n\n\texport let trimDuration = 0;\n\n\tlet playbackSpeeds = [0.5, 1, 1.5, 2];\n\tlet playbackSpeed = playbackSpeeds[1];\n\n\tlet trimRegion: RegionsPlugin | null = null;\n\tlet activeRegion: Region | null = null;\n\n\tlet leftRegionHandle: HTMLDivElement | null;\n\tlet rightRegionHandle: HTMLDivElement | null;\n\tlet activeHandle = \"\";\n\n\tlet currentVolume = 1;\n\n\t$: trimRegion =\n\t\tcontainer && waveform\n\t\t\t? waveform.registerPlugin(RegionsPlugin.create())\n\t\t\t: null;\n\n\t$: trimRegion?.on(\"region-out\", (region) => {\n\t\tregion.play();\n\t});\n\n\t$: trimRegion?.on(\"region-updated\", (region) => {\n\t\ttrimDuration = region.end - region.start;\n\t});\n\n\t$: trimRegion?.on(\"region-clicked\", (region, e) => {\n\t\te.stopPropagation(); // prevent triggering a click on the waveform\n\t\tactiveRegion = region;\n\t\tregion.play();\n\t});\n\n\tconst addTrimRegion = (): void => {\n\t\tif (!trimRegion) return;\n\t\tactiveRegion = trimRegion?.addRegion({\n\t\t\tstart: audio_duration / 4,\n\t\t\tend: audio_duration / 2,\n\t\t\t...trim_region_settings\n\t\t});\n\n\t\ttrimDuration = activeRegion.end - activeRegion.start;\n\t};\n\n\t$: if (activeRegion) {\n\t\tconst shadowRoot = container.children[0]!.shadowRoot!;\n\n\t\trightRegionHandle = shadowRoot.querySelector('[data-resize=\"right\"]');\n\t\tleftRegionHandle = shadowRoot.querySelector('[data-resize=\"left\"]');\n\n\t\tif (leftRegionHandle && rightRegionHandle) {\n\t\t\tleftRegionHandle.setAttribute(\"role\", \"button\");\n\t\t\trightRegionHandle.setAttribute(\"role\", \"button\");\n\t\t\tleftRegionHandle?.setAttribute(\"aria-label\", \"Drag to adjust start time\");\n\t\t\trightRegionHandle?.setAttribute(\"aria-label\", \"Drag to adjust end time\");\n\t\t\tleftRegionHandle?.setAttribute(\"tabindex\", \"0\");\n\t\t\trightRegionHandle?.setAttribute(\"tabindex\", \"0\");\n\n\t\t\tleftRegionHandle.addEventListener(\"focus\", () => {\n\t\t\t\tif (trimRegion) activeHandle = \"left\";\n\t\t\t});\n\n\t\t\trightRegionHandle.addEventListener(\"focus\", () => {\n\t\t\t\tif (trimRegion) activeHandle = \"right\";\n\t\t\t});\n\t\t}\n\t}\n\n\tconst trimAudio = (): void => {\n\t\tif (waveform && trimRegion) {\n\t\t\tif (activeRegion) {\n\t\t\t\tconst start = activeRegion.start;\n\t\t\t\tconst end = activeRegion.end;\n\t\t\t\thandle_trim_audio(start, end);\n\t\t\t\tmode = \"\";\n\t\t\t\tactiveRegion = null;\n\t\t\t}\n\t\t}\n\t};\n\n\tconst clearRegions = (): void => {\n\t\ttrimRegion?.getRegions().forEach((region) => {\n\t\t\tregion.remove();\n\t\t});\n\t\ttrimRegion?.clearRegions();\n\t};\n\n\tconst toggleTrimmingMode = (): void => {\n\t\tclearRegions();\n\t\tif (mode === \"edit\") {\n\t\t\tmode = \"\";\n\t\t} else {\n\t\t\tmode = \"edit\";\n\t\t\taddTrimRegion();\n\t\t}\n\t};\n\n\tconst adjustRegionHandles = (handle: string, key: string): void => {\n\t\tlet newStart;\n\t\tlet newEnd;\n\n\t\tif (!activeRegion) return;\n\t\tif (handle === \"left\") {\n\t\t\tif (key === \"ArrowLeft\") {\n\t\t\t\tnewStart = activeRegion.start - 0.05;\n\t\t\t\tnewEnd = activeRegion.end;\n\t\t\t} else {\n\t\t\t\tnewStart = activeRegion.start + 0.05;\n\t\t\t\tnewEnd = activeRegion.end;\n\t\t\t}\n\t\t} else {\n\t\t\tif (key === \"ArrowLeft\") {\n\t\t\t\tnewStart = activeRegion.start;\n\t\t\t\tnewEnd = activeRegion.end - 0.05;\n\t\t\t} else {\n\t\t\t\tnewStart = activeRegion.start;\n\t\t\t\tnewEnd = activeRegion.end + 0.05;\n\t\t\t}\n\t\t}\n\n\t\tactiveRegion.setOptions({\n\t\t\tstart: newStart,\n\t\t\tend: newEnd\n\t\t});\n\n\t\ttrimDuration = activeRegion.end - activeRegion.start;\n\t};\n\n\t$: trimRegion &&\n\t\twindow.addEventListener(\"keydown\", (e) => {\n\t\t\tif (e.key === \"ArrowLeft\") {\n\t\t\t\tadjustRegionHandles(activeHandle, \"ArrowLeft\");\n\t\t\t} else if (e.key === \"ArrowRight\") {\n\t\t\t\tadjustRegionHandles(activeHandle, \"ArrowRight\");\n\t\t\t}\n\t\t});\n</script>\n\n<div class=\"controls\" data-testid=\"waveform-controls\">\n\t<div class=\"control-wrapper\">\n\t\t<button\n\t\t\tclass=\"action icon volume\"\n\t\t\tstyle:color={show_volume_slider\n\t\t\t\t? \"var(--color-accent)\"\n\t\t\t\t: \"var(--neutral-400)\"}\n\t\t\taria-label=\"Adjust volume\"\n\t\t\ton:click={() => (show_volume_slider = !show_volume_slider)}\n\t\t>\n\t\t\t<VolumeLevels {currentVolume} />\n\t\t</button>\n\n\t\t{#if show_volume_slider}\n\t\t\t<VolumeControl bind:currentVolume bind:show_volume_slider {waveform} />\n\t\t{/if}\n\n\t\t<button\n\t\t\tclass:hidden={show_volume_slider}\n\t\t\tclass=\"playback icon\"\n\t\t\taria-label={`Adjust playback speed to ${\n\t\t\t\tplaybackSpeeds[\n\t\t\t\t\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\n\t\t\t\t]\n\t\t\t}x`}\n\t\t\ton:click={() => {\n\t\t\t\tplaybackSpeed =\n\t\t\t\t\tplaybackSpeeds[\n\t\t\t\t\t\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\n\t\t\t\t\t];\n\n\t\t\t\twaveform?.setPlaybackRate(playbackSpeed);\n\t\t\t}}\n\t\t>\n\t\t\t<span>{playbackSpeed}x</span>\n\t\t</button>\n\t</div>\n\n\t<div class=\"play-pause-wrapper\">\n\t\t<button\n\t\t\tclass=\"rewind icon\"\n\t\t\taria-label={`Skip backwards by ${get_skip_rewind_amount(\n\t\t\t\taudio_duration,\n\t\t\t\twaveform_options.skip_length\n\t\t\t)} seconds`}\n\t\t\ton:click={() =>\n\t\t\t\twaveform?.skip(\n\t\t\t\t\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length) *\n\t\t\t\t\t\t-1\n\t\t\t\t)}\n\t\t>\n\t\t\t<Backward />\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"play-pause-button icon\"\n\t\t\ton:click={() => waveform?.playPause()}\n\t\t\taria-label={playing ? i18n(\"audio.pause\") : i18n(\"audio.play\")}\n\t\t>\n\t\t\t{#if playing}\n\t\t\t\t<Pause />\n\t\t\t{:else}\n\t\t\t\t<Play />\n\t\t\t{/if}\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"skip icon\"\n\t\t\taria-label=\"Skip forward by {get_skip_rewind_amount(\n\t\t\t\taudio_duration,\n\t\t\t\twaveform_options.skip_length\n\t\t\t)} seconds\"\n\t\t\ton:click={() =>\n\t\t\t\twaveform?.skip(\n\t\t\t\t\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length)\n\t\t\t\t)}\n\t\t>\n\t\t\t<Forward />\n\t\t</button>\n\t</div>\n\n\t<div class=\"settings-wrapper\">\n\t\t{#if editable && interactive}\n\t\t\t{#if show_redo && mode === \"\"}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"action icon\"\n\t\t\t\t\taria-label=\"Reset audio\"\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\thandle_reset_value();\n\t\t\t\t\t\tclearRegions();\n\t\t\t\t\t\tmode = \"\";\n\t\t\t\t\t}}\n\t\t\t\t>\n\t\t\t\t\t<Undo />\n\t\t\t\t</button>\n\t\t\t{/if}\n\n\t\t\t{#if mode === \"\"}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"action icon\"\n\t\t\t\t\taria-label=\"Trim audio to selection\"\n\t\t\t\t\ton:click={toggleTrimmingMode}\n\t\t\t\t>\n\t\t\t\t\t<Trim />\n\t\t\t\t</button>\n\t\t\t{:else}\n\t\t\t\t<button class=\"text-button\" on:click={trimAudio}>Trim</button>\n\t\t\t\t<button class=\"text-button\" on:click={toggleTrimmingMode}>Cancel</button\n\t\t\t\t>\n\t\t\t{/if}\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.settings-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-self: self-end;\n\t\talign-items: center;\n\t\tgrid-area: editing;\n\t}\n\t.text-button {\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-sm);\n\t\tfont-weight: 300;\n\t\tfont-size: var(--size-3);\n\t\ttext-align: center;\n\t\tcolor: var(--neutral-400);\n\t\theight: var(--size-5);\n\t\tfont-weight: bold;\n\t\tpadding: 0 5px;\n\t\tmargin-left: 5px;\n\t}\n\n\t.text-button:hover,\n\t.text-button:focus {\n\t\tcolor: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.controls {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: 1fr 1fr 1fr;\n\t\tgrid-template-areas: \"controls playback editing\";\n\t\tmargin-top: 5px;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: space-between;\n\t}\n\t.controls div {\n\t\tmargin: var(--size-1) 0;\n\t}\n\n\t@media (max-width: 600px) {\n\t\t.controls {\n\t\t\tgrid-template-columns: 1fr 1fr;\n\t\t\tgrid-template-rows: auto auto;\n\t\t\tgrid-template-areas:\n\t\t\t\t\"playback playback\"\n\t\t\t\t\"controls editing\";\n\t\t}\n\t}\n\n\t@media (max-width: 319px) {\n\t\t.controls {\n\t\t\toverflow-x: scroll;\n\t\t}\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.control-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-self: self-start;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tgrid-area: controls;\n\t}\n\n\t.action {\n\t\twidth: var(--size-5);\n\t\tcolor: var(--neutral-400);\n\t\tmargin-left: var(--spacing-md);\n\t}\n\t.icon:hover,\n\t.icon:focus {\n\t\tcolor: var(--color-accent);\n\t}\n\t.play-pause-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-self: center;\n\t\tgrid-area: playback;\n\t}\n\n\t@media (max-width: 600px) {\n\t\t.play-pause-wrapper {\n\t\t\tmargin: var(--spacing-md);\n\t\t}\n\t}\n\t.playback {\n\t\tborder: 1px solid var(--neutral-400);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: 5.5ch;\n\t\tfont-weight: 300;\n\t\tfont-size: var(--size-3);\n\t\ttext-align: center;\n\t\tcolor: var(--neutral-400);\n\t\theight: var(--size-5);\n\t\tfont-weight: bold;\n\t}\n\n\t.playback:hover,\n\t.playback:focus {\n\t\tcolor: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.rewind,\n\t.skip {\n\t\tmargin: 0 10px;\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.play-pause-button {\n\t\twidth: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--neutral-400);\n\t\tfill: var(--neutral-400);\n\t}\n\n\t.volume {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-right: var(--spacing-xl);\n\t\twidth: var(--size-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { Music } from \"@gradio/icons\";\n\timport { format_time, type I18nFormatter } from \"@gradio/utils\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport { skip_audio, process_audio } from \"../shared/utils\";\n\timport WaveformControls from \"../shared/WaveformControls.svelte\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value: null | FileData = null;\n\t$: url = value?.url;\n\texport let label: string;\n\texport let i18n: I18nFormatter;\n\texport let dispatch_blob: (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t) => Promise<void> = () => Promise.resolve();\n\texport let interactive = false;\n\texport let editable = true;\n\texport let trim_region_settings = {};\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions;\n\texport let mode = \"\";\n\texport let loop: boolean;\n\texport let handle_reset_value: () => void = () => {};\n\n\tlet container: HTMLDivElement;\n\tlet waveform: WaveSurfer | undefined;\n\tlet playing = false;\n\n\tlet timeRef: HTMLTimeElement;\n\tlet durationRef: HTMLTimeElement;\n\tlet audio_duration: number;\n\n\tlet trimDuration = 0;\n\n\tlet show_volume_slider = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstop: undefined;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tedit: undefined;\n\t\tend: undefined;\n\t\tload: undefined;\n\t}>();\n\n\tconst create_waveform = (): void => {\n\t\twaveform = WaveSurfer.create({\n\t\t\tcontainer: container,\n\t\t\t...waveform_settings\n\t\t});\n\t\tresolve_wasm_src(value?.url).then((resolved_src) => {\n\t\t\tif (resolved_src && waveform) {\n\t\t\t\treturn waveform.load(resolved_src);\n\t\t\t}\n\t\t});\n\t};\n\n\t$: if (container !== undefined) {\n\t\tif (waveform !== undefined) waveform.destroy();\n\t\tcontainer.innerHTML = \"\";\n\t\tcreate_waveform();\n\t\tplaying = false;\n\t}\n\n\t$: waveform?.on(\"decode\", (duration: any) => {\n\t\taudio_duration = duration;\n\t\tdurationRef && (durationRef.textContent = format_time(duration));\n\t});\n\n\t$: waveform?.on(\n\t\t\"timeupdate\",\n\t\t(currentTime: any) =>\n\t\t\ttimeRef && (timeRef.textContent = format_time(currentTime))\n\t);\n\n\t$: waveform?.on(\"ready\", () => {\n\t\tif (!waveform_settings.autoplay) {\n\t\t\twaveform?.stop();\n\t\t} else {\n\t\t\twaveform?.play();\n\t\t}\n\t});\n\n\t$: waveform?.on(\"finish\", () => {\n\t\tif (loop) {\n\t\t\twaveform?.play();\n\t\t} else {\n\t\t\tplaying = false;\n\t\t\tdispatch(\"stop\");\n\t\t}\n\t});\n\t$: waveform?.on(\"pause\", () => {\n\t\tplaying = false;\n\t\tdispatch(\"pause\");\n\t});\n\t$: waveform?.on(\"play\", () => {\n\t\tplaying = true;\n\t\tdispatch(\"play\");\n\t});\n\n\t$: waveform?.on(\"load\", () => {\n\t\tdispatch(\"load\");\n\t});\n\n\tconst handle_trim_audio = async (\n\t\tstart: number,\n\t\tend: number\n\t): Promise<void> => {\n\t\tmode = \"\";\n\t\tconst decodedData = waveform?.getDecodedData();\n\t\tif (decodedData)\n\t\t\tawait process_audio(\n\t\t\t\tdecodedData,\n\t\t\t\tstart,\n\t\t\t\tend,\n\t\t\t\twaveform_settings.sampleRate\n\t\t\t).then(async (trimmedBlob: Uint8Array) => {\n\t\t\t\tawait dispatch_blob([trimmedBlob], \"change\");\n\t\t\t\twaveform?.destroy();\n\t\t\t\tcontainer.innerHTML = \"\";\n\t\t\t});\n\t\tdispatch(\"edit\");\n\t};\n\n\tasync function load_audio(data: string): Promise<void> {\n\t\tawait resolve_wasm_src(data).then((resolved_src) => {\n\t\t\tif (!resolved_src || value?.is_stream) return;\n\t\t\treturn waveform?.load(resolved_src);\n\t\t});\n\t}\n\n\t$: url && load_audio(url);\n\n\tonMount(() => {\n\t\twindow.addEventListener(\"keydown\", (e) => {\n\t\t\tif (!waveform || show_volume_slider) return;\n\t\t\tif (e.key === \"ArrowRight\" && mode !== \"edit\") {\n\t\t\t\tskip_audio(waveform, 0.1);\n\t\t\t} else if (e.key === \"ArrowLeft\" && mode !== \"edit\") {\n\t\t\t\tskip_audio(waveform, -0.1);\n\t\t\t}\n\t\t});\n\t});\n</script>\n\n{#if value === null}\n\t<Empty size=\"small\">\n\t\t<Music />\n\t</Empty>\n{:else if value.is_stream}\n\t<audio\n\t\tclass=\"standard-player\"\n\t\tsrc={value.url}\n\t\tcontrols\n\t\tautoplay={waveform_settings.autoplay}\n\t\t{loop}\n\t\ton:load\n\t/>\n{:else}\n\t<div\n\t\tclass=\"component-wrapper\"\n\t\tdata-testid={label ? \"waveform-\" + label : \"unlabelled-audio\"}\n\t>\n\t\t<div class=\"waveform-container\">\n\t\t\t<div\n\t\t\t\tid=\"waveform\"\n\t\t\t\tbind:this={container}\n\t\t\t\tstyle:height={container ? null : \"58px\"}\n\t\t\t/>\n\t\t</div>\n\n\t\t<div class=\"timestamps\">\n\t\t\t<time bind:this={timeRef} id=\"time\">0:00</time>\n\t\t\t<div>\n\t\t\t\t{#if mode === \"edit\" && trimDuration > 0}\n\t\t\t\t\t<time id=\"trim-duration\">{format_time(trimDuration)}</time>\n\t\t\t\t{/if}\n\t\t\t\t<time bind:this={durationRef} id=\"duration\">0:00</time>\n\t\t\t</div>\n\t\t</div>\n\n\t\t<!-- {#if waveform} -->\n\t\t<WaveformControls\n\t\t\t{container}\n\t\t\t{waveform}\n\t\t\t{playing}\n\t\t\t{audio_duration}\n\t\t\t{i18n}\n\t\t\t{interactive}\n\t\t\t{handle_trim_audio}\n\t\t\tbind:mode\n\t\t\tbind:trimDuration\n\t\t\tbind:show_volume_slider\n\t\t\tshow_redo={interactive}\n\t\t\t{handle_reset_value}\n\t\t\t{waveform_options}\n\t\t\t{trim_region_settings}\n\t\t\t{editable}\n\t\t/>\n\t\t<!-- {/if} -->\n\t</div>\n{/if}\n\n<style>\n\t.component-wrapper {\n\t\tpadding: var(--size-3);\n\t\twidth: 100%;\n\t}\n\n\t:global(::part(wrapper)) {\n\t\tmargin-bottom: var(--size-2);\n\t}\n\n\t.timestamps {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tpadding: var(--size-1) 0;\n\t}\n\n\t#time {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t#duration {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t#trim-duration {\n\t\tcolor: var(--color-accent);\n\t\tmargin-right: var(--spacing-sm);\n\t}\n\t.waveform-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: var(--size-full);\n\t}\n\n\t#waveform {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: relative;\n\t}\n\n\t.standard-player {\n\t\twidth: 100%;\n\t\tpadding: var(--size-2);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { uploadToHuggingFace } from \"@gradio/utils\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport { ShareButton, IconButton, BlockLabel } from \"@gradio/atoms\";\n\timport { Download, Music } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport AudioPlayer from \"../player/AudioPlayer.svelte\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\n\texport let value: null | FileData = null;\n\texport let label: string;\n\texport let show_label = true;\n\texport let show_download_button = true;\n\texport let show_share_button = false;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions;\n\texport let editable = true;\n\texport let loop: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tstop: undefined;\n\t}>();\n\n\t$: value && dispatch(\"change\", value);\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={false}\n\tlabel={label || i18n(\"audio.audio\")}\n/>\n\n{#if value !== null}\n\t<div class=\"icon-buttons\">\n\t\t{#if show_download_button}\n\t\t\t<DownloadLink href={value.url} download={value.orig_name || value.path}>\n\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t</DownloadLink>\n\t\t{/if}\n\t\t{#if show_share_button}\n\t\t\t<ShareButton\n\t\t\t\t{i18n}\n\t\t\t\ton:error\n\t\t\t\ton:share\n\t\t\t\tformatter={async (value) => {\n\t\t\t\t\tif (!value) return \"\";\n\t\t\t\t\tlet url = await uploadToHuggingFace(value.url, \"url\");\n\t\t\t\t\treturn `<audio controls src=\"${url}\"></audio>`;\n\t\t\t\t}}\n\t\t\t\t{value}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t<AudioPlayer\n\t\t{value}\n\t\t{label}\n\t\t{i18n}\n\t\t{waveform_settings}\n\t\t{waveform_options}\n\t\t{editable}\n\t\t{loop}\n\t\ton:pause\n\t\ton:play\n\t\ton:stop\n\t\ton:load\n\t/>\n{:else}\n\t<Empty size=\"small\">\n\t\t<Music />\n\t</Empty>\n{/if}\n\n<style>\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: 6px;\n\t\tright: 6px;\n\t\tgap: var(--size-1);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "title", "path0", "path1", "path2", "g", "defs", "clipPath", "rect", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "reject", "fulfilled", "step", "e", "rejected", "result", "decode", "audioData", "sampleRate", "audioCtx", "normalize", "channelData", "firstChannel", "n", "length", "max", "i", "absN", "channel", "createBuffer", "duration", "Decoder", "fetchBlob", "url", "progressCallback", "requestInit", "_a", "_b", "response", "reader", "contentLength", "<PERSON><PERSON><PERSON><PERSON>", "processChunk", "done", "percentage", "<PERSON><PERSON><PERSON>", "EventEmitter", "event", "listener", "options", "unsubscribeOnce", "eventName", "args", "Player", "callback", "src", "blob", "newSrc", "element", "time", "volume", "muted", "rate", "<PERSON><PERSON><PERSON>", "sinkId", "makeDraggable", "onDrag", "onStart", "onEnd", "threshold", "unsub", "down", "startX", "startY", "isDragging", "move", "x", "y", "left", "top", "click", "up", "<PERSON><PERSON><PERSON>", "audioElement", "parent", "div", "shadow", "container", "getClickPosition", "relativeX", "relativeY", "scrollLeft", "scrollWidth", "clientWidth", "endX", "delay", "_", "__", "newParent", "delayMs", "context", "color", "canvasElement", "gradient", "colorStopPercentage", "index", "offset", "ctx", "vScale", "topChannel", "bottomChannel", "width", "height", "halfHeight", "pixelRatio", "<PERSON><PERSON><PERSON><PERSON>", "barGap", "barRadius", "barIndexScale", "rectFn", "prevX", "maxTop", "maxBottom", "topBarHeight", "bottomBarHeight", "barHeight", "magnitudeTop", "magnitudeBottom", "_options", "drawChannel", "hScale", "h", "start", "end", "canvasContainer", "progressContainer", "canvas", "progressCanvas", "progressCtx", "len", "scale", "viewportWidth", "totalBarWidth", "viewportLen", "draw", "<PERSON><PERSON><PERSON><PERSON>", "tail<PERSON>ela<PERSON>", "renderHead", "fromIndex", "toIndex", "renderTail", "parentWidth", "useParentWidth", "channels", "oldCursorPosition", "newCursortPosition", "minPxPerSec", "progress", "isPlaying", "progressWidth", "center", "minScroll", "minDiff", "percents", "Timer", "WebAudioPlayer", "audioContext", "arrayBuffer", "audioBuffer", "deviceId", "defaultOptions", "WaveSurfer", "media", "currentTime", "debounce", "plugin", "unsubscribe", "p", "onProgress", "max<PERSON><PERSON><PERSON>", "precision", "maxChannels", "peaks", "data", "sampleSize", "sample", "isInteractive", "seconds", "audioBufferToWav", "numOfChan", "buffer", "view", "writeString", "string", "process_audio", "waveform_sample_rate", "numberOfChannels", "<PERSON><PERSON><PERSON><PERSON>", "startOffset", "trimmedAudioBuffer", "trimmedData", "skip_audio", "waveform", "amount", "get_skip_rewind_amount", "audio_duration", "skip_length", "t", "s", "o", "d", "r", "u", "c", "a", "l", "currentVolume", "$$props", "onMount", "input", "show_volume_slider", "volumeElement", "adjustSlider", "slider", "$$value", "focusout_handler", "$$invalidate", "create_if_block_2", "button", "button0", "button1", "create_if_block_4", "if_block2", "create_if_block", "set_style", "attr", "button2", "button2_aria_label_value", "button4", "button4_aria_label_value", "div3", "div0", "span", "div1", "button3", "div2", "current", "dirty", "i18n", "playing", "show_redo", "interactive", "handle_trim_audio", "mode", "handle_reset_value", "waveform_options", "trim_region_settings", "editable", "trimDuration", "playbackSpeeds", "playbackSpeed", "trimRegion", "activeRegion", "leftRegionHandle", "rightRegionHandle", "activeHandle", "addTrimRegion", "trimAudio", "clearRegions", "region", "toggleTrimmingMode", "adjustRegionHandles", "handle", "key", "newStart", "newEnd", "click_handler", "click_handler_3", "RegionsPlugin", "shadowRoot", "createEventDispatcher", "if_block", "div4", "time0", "time1", "src_url_equal", "audio", "audio_src_value", "audio_autoplay_value", "t_value", "format_time", "set_data", "label", "dispatch_blob", "waveform_settings", "loop", "timeRef", "durationRef", "dispatch", "create_waveform", "resolve_wasm_src", "resolved_src", "decodedData", "trimmedBlob", "load_audio", "create_if_block_1", "downloadlink_changes", "Download", "iconbutton_changes", "Music", "show_label", "show_download_button", "show_share_button", "uploadToHuggingFace"], "mappings": "qtCAAAA,GAeAC,EAAAC,EAAAC,CAAA,EAPEC,GAMCF,EAAAG,CAAA,u0BCdHL,GAeAC,EAAAC,EAAAC,CAAA,EAPEC,GAMCF,EAAAG,CAAA,wUCJK,YAAU,+uBAVlBL,GAoBAC,EAAAC,EAAAC,CAAA,EAVCC,GAAwBF,EAAAI,CAAA,UACxBF,GAKEF,EAAAK,CAAA,EAAAH,GAGAF,EAAAM,CAAA,0UCTK,aAAW,w5BAVnBR,GAyBAC,EAAAC,EAAAC,CAAA,EAfCC,GAAyBF,EAAAI,CAAA,UACzBF,GAGEF,EAAAK,CAAA,EAAAH,GAKAF,EAAAM,CAAA,EAAAJ,GAKAF,EAAAO,CAAA,gVCdK,cAAY,+6BAVpBT,GA0BAC,EAAAC,EAAAC,CAAA,EAhBCC,GAA0BF,EAAAI,CAAA,UAC1BF,GAUCF,EAAAQ,CAAA,EATCN,GAKCM,EAAAH,CAAA,EAAAH,GAGAM,EAAAF,CAAA,EACFJ,GAIAF,EAAAS,CAAA,EAHCP,GAEAO,EAAAC,CAAA,EADCR,GAA4CQ,EAAAC,CAAA,uGCvBhD,IAAIC,GAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,CAAA,GAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EAEA,SAASY,GAAOC,EAAWC,EAAY,CACnC,OAAOhB,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,MAAMiB,EAAW,IAAI,aAAa,CAAE,WAAAD,CAAY,CAAA,EAEhD,OADeC,EAAS,gBAAgBF,CAAS,EACnC,QAAQ,IAAME,EAAS,MAAO,CAAA,CACpD,CAAK,CACL,CAEA,SAASC,GAAUC,EAAa,CAC5B,MAAMC,EAAeD,EAAY,CAAC,EAClC,GAAIC,EAAa,KAAMC,GAAMA,EAAI,GAAKA,EAAI,EAAE,EAAG,CAC3C,MAAMC,EAASF,EAAa,OAC5B,IAAIG,EAAM,EACV,QAASC,EAAI,EAAGA,EAAIF,EAAQE,IAAK,CAC7B,MAAMC,EAAO,KAAK,IAAIL,EAAaI,CAAC,CAAC,EACjCC,EAAOF,IACPA,EAAME,EACb,CACD,UAAWC,KAAWP,EAClB,QAASK,EAAI,EAAGA,EAAIF,EAAQE,IACxBE,EAAQF,CAAC,GAAKD,CAGzB,CACD,OAAOJ,CACX,CAEA,SAASQ,GAAaR,EAAaS,EAAU,CAEzC,OAAI,OAAOT,EAAY,CAAC,GAAM,WAC1BA,EAAc,CAACA,CAAW,GAE9BD,GAAUC,CAAW,EACd,CACH,SAAAS,EACA,OAAQT,EAAY,CAAC,EAAE,OACvB,WAAYA,EAAY,CAAC,EAAE,OAASS,EACpC,iBAAkBT,EAAY,OAC9B,eAAiBK,GAAgEL,IAAYK,CAAC,EAC9F,gBAAiB,YAAY,UAAU,gBACvC,cAAe,YAAY,UAAU,aAC7C,CACA,CACA,MAAMK,GAAU,CACZ,OAAAf,GACA,aAAAa,EACJ,ECxDA,IAAI3B,GAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,CAAA,GAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EACA,SAAS4B,GAAUC,EAAKC,EAAkBC,EAAa,CACnD,IAAIC,EAAIC,EACR,OAAOnC,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAEhD,MAAMoC,EAAW,MAAM,MAAML,EAAKE,CAAW,EAE7C,CACI,MAAMI,GAAUH,EAAKE,EAAS,MAAO,EAAC,QAAU,MAAQF,IAAO,OAAS,OAASA,EAAG,UAAS,EACvFI,EAAgB,QAAQH,EAAKC,EAAS,WAAa,MAAQD,IAAO,OAAS,OAASA,EAAG,IAAI,gBAAgB,CAAC,EAClH,IAAII,EAAiB,EAErB,MAAMC,EAAe,CAACC,EAAMnC,IAAUN,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAC/E,GAAIyC,EACA,OAEJF,GAAiEjC,GAAM,QAAW,EAClF,MAAMoC,EAAa,KAAK,MAAOH,EAAiBD,EAAiB,GAAG,EACpE,OAAAN,EAAiBU,CAAU,EAE4BL,GAAO,KAAI,EAAG,KAAK,CAAC,CAAE,KAAAI,EAAM,MAAAnC,CAAK,IAAOkC,EAAaC,EAAMnC,CAAK,CAAC,CACxI,CAAa,EAC+C+B,GAAO,KAAI,EAAG,KAAK,CAAC,CAAE,KAAAI,EAAM,MAAAnC,CAAK,IAAOkC,EAAaC,EAAMnC,CAAK,CAAC,CACpH,CACD,OAAO8B,EAAS,MACxB,CAAK,CACL,CACA,MAAMO,GAAU,CACZ,UAAAb,EACJ,ECpCA,MAAMc,EAAa,CACf,aAAc,CACV,KAAK,UAAY,GAEjB,KAAK,GAAK,KAAK,iBAEf,KAAK,GAAK,KAAK,mBAClB,CAED,iBAAiBC,EAAOC,EAAUC,EAAS,CAKvC,GAJK,KAAK,UAAUF,CAAK,IACrB,KAAK,UAAUA,CAAK,EAAI,IAAI,KAEhC,KAAK,UAAUA,CAAK,EAAE,IAAIC,CAAQ,EACoBC,GAAQ,KAAM,CAChE,MAAMC,EAAkB,IAAM,CAC1B,KAAK,oBAAoBH,EAAOG,CAAe,EAC/C,KAAK,oBAAoBH,EAAOC,CAAQ,CACxD,EACY,YAAK,iBAAiBD,EAAOG,CAAe,EACrCA,CACV,CACD,MAAO,IAAM,KAAK,oBAAoBH,EAAOC,CAAQ,CACxD,CACD,oBAAoBD,EAAOC,EAAU,CACjC,IAAIZ,GACHA,EAAK,KAAK,UAAUW,CAAK,KAAO,MAAQX,IAAO,QAAkBA,EAAG,OAAOY,CAAQ,CACvF,CAED,KAAKD,EAAOC,EAAU,CAClB,OAAO,KAAK,GAAGD,EAAOC,EAAU,CAAE,KAAM,EAAI,CAAE,CACjD,CAED,OAAQ,CACJ,KAAK,UAAY,EACpB,CAED,KAAKG,KAAcC,EAAM,CACjB,KAAK,UAAUD,CAAS,GACxB,KAAK,UAAUA,CAAS,EAAE,QAASH,GAAaA,EAAS,GAAGI,CAAI,CAAC,CAExE,CACL,CC1CA,MAAMC,WAAeP,EAAa,CAC9B,YAAYG,EAAS,CACjB,QACA,KAAK,gBAAkB,GACnBA,EAAQ,OACR,KAAK,MAAQA,EAAQ,MACrB,KAAK,gBAAkB,IAGvB,KAAK,MAAQ,SAAS,cAAc,OAAO,EAG3CA,EAAQ,gBACR,KAAK,MAAM,SAAW,IAGtBA,EAAQ,WACR,KAAK,MAAM,SAAW,IAGtBA,EAAQ,cAAgB,MACxB,KAAK,eAAe,UAAW,IAAM,CAC7BA,EAAQ,cAAgB,OACxB,KAAK,MAAM,aAAeA,EAAQ,aAEtD,CAAa,CAER,CACD,aAAaF,EAAOO,EAAUL,EAAS,CACnC,YAAK,MAAM,iBAAiBF,EAAOO,EAAUL,CAAO,EAC7C,IAAM,KAAK,MAAM,oBAAoBF,EAAOO,CAAQ,CAC9D,CACD,eAAeP,EAAOO,EAAU,CAC5B,OAAO,KAAK,aAAaP,EAAOO,EAAU,CAAE,KAAM,EAAI,CAAE,CAC3D,CACD,QAAS,CACL,OAAO,KAAK,MAAM,YAAc,KAAK,MAAM,KAAO,EACrD,CACD,WAAY,CACR,MAAMC,EAAM,KAAK,SACbA,EAAI,WAAW,OAAO,GACtB,IAAI,gBAAgBA,CAAG,CAE9B,CACD,OAAOtB,EAAKuB,EAAM,CAEd,GADY,KAAK,WACLvB,EACR,OACJ,KAAK,UAAS,EACd,MAAMwB,EAASD,aAAgB,KAAO,IAAI,gBAAgBA,CAAI,EAAIvB,EAClE,KAAK,MAAM,IAAMwB,EACjB,KAAK,MAAM,MACd,CACD,SAAU,CACN,KAAK,MAAM,QACP,MAAK,kBAET,KAAK,MAAM,SACX,KAAK,UAAS,EACd,KAAK,MAAM,IAAM,GAEjB,KAAK,MAAM,OACd,CACD,gBAAgBC,EAAS,CACrB,KAAK,MAAQA,CAChB,CAED,MAAO,CACH,OAAO,KAAK,MAAM,MACrB,CAED,OAAQ,CACJ,KAAK,MAAM,OACd,CAED,WAAY,CACR,MAAO,CAAC,KAAK,MAAM,QAAU,CAAC,KAAK,MAAM,KAC5C,CAED,QAAQC,EAAM,CACV,KAAK,MAAM,YAAcA,CAC5B,CAED,aAAc,CACV,OAAO,KAAK,MAAM,QACrB,CAED,gBAAiB,CACb,OAAO,KAAK,MAAM,WACrB,CAED,WAAY,CACR,OAAO,KAAK,MAAM,MACrB,CAED,UAAUC,EAAQ,CACd,KAAK,MAAM,OAASA,CACvB,CAED,UAAW,CACP,OAAO,KAAK,MAAM,KACrB,CAED,SAASC,EAAO,CACZ,KAAK,MAAM,MAAQA,CACtB,CAED,iBAAkB,CACd,OAAO,KAAK,MAAM,YACrB,CAED,gBAAgBC,EAAMC,EAAe,CAE7BA,GAAiB,OACjB,KAAK,MAAM,eAAiBA,GAEhC,KAAK,MAAM,aAAeD,CAC7B,CAED,iBAAkB,CACd,OAAO,KAAK,KACf,CAED,UAAUE,EAAQ,CAGd,OADc,KAAK,MACN,UAAUA,CAAM,CAChC,CACL,CCjIO,SAASC,GAAcP,EAASQ,EAAQC,EAASC,EAAOC,EAAY,EAAG,CAC1E,IAAIC,EAAQ,IAAM,CAEtB,EACI,GAAI,CAACZ,EACD,OAAOY,EACX,MAAMC,EAAQ1D,GAAM,CAEhB,GAAIA,EAAE,SAAW,EACb,OACJA,EAAE,eAAc,EAChBA,EAAE,gBAAe,EACjB6C,EAAQ,MAAM,YAAc,OAC5B,IAAIc,EAAS3D,EAAE,QACX4D,EAAS5D,EAAE,QACX6D,EAAa,GACjB,MAAMC,EAAQ9D,GAAM,CAChBA,EAAE,eAAc,EAChBA,EAAE,gBAAe,EACjB,MAAM+D,EAAI/D,EAAE,QACNgE,EAAIhE,EAAE,QACZ,GAAI6D,GAAc,KAAK,IAAIE,EAAIJ,CAAM,GAAKH,GAAa,KAAK,IAAIQ,EAAIJ,CAAM,GAAKJ,EAAW,CACtF,KAAM,CAAE,KAAAS,EAAM,IAAAC,CAAK,EAAGrB,EAAQ,sBAAqB,EAC9CgB,IACDA,EAAa,GACqCP,IAAQK,EAASM,EAAML,EAASM,CAAG,GAEzFb,EAAOU,EAAIJ,EAAQK,EAAIJ,EAAQG,EAAIE,EAAMD,EAAIE,CAAG,EAChDP,EAASI,EACTH,EAASI,CACZ,CACb,EACcG,EAASnE,GAAM,CACb6D,IACA7D,EAAE,eAAc,EAChBA,EAAE,gBAAe,EAEjC,EACcoE,EAAK,IAAM,CACbvB,EAAQ,MAAM,YAAc,GACxBgB,GAC8CN,MAElDE,GACZ,EACQ,SAAS,iBAAiB,cAAeK,CAAI,EAC7C,SAAS,iBAAiB,YAAaM,CAAE,EACzC,SAAS,iBAAiB,eAAgBA,CAAE,EAC5C,SAAS,iBAAiB,QAASD,EAAO,EAAI,EAC9CV,EAAQ,IAAM,CACV,SAAS,oBAAoB,cAAeK,CAAI,EAChD,SAAS,oBAAoB,YAAaM,CAAE,EAC5C,SAAS,oBAAoB,eAAgBA,CAAE,EAC/C,WAAW,IAAM,CACb,SAAS,oBAAoB,QAASD,EAAO,EAAI,CACpD,EAAE,EAAE,CACjB,CACA,EACI,OAAAtB,EAAQ,iBAAiB,cAAea,CAAI,EACrC,IAAM,CACTD,IACAZ,EAAQ,oBAAoB,cAAea,CAAI,CACvD,CACA,CC7DA,MAAMW,WAAiBpC,EAAa,CAChC,YAAYG,EAASkC,EAAc,CAC/B,QACA,KAAK,SAAW,GAChB,KAAK,YAAc,GACnB,KAAK,UAAY,KACjB,KAAK,eAAiB,KACtB,KAAK,WAAa,GAClB,KAAK,QAAUlC,EACf,MAAMmC,EAAS,KAAK,2BAA2BnC,EAAQ,SAAS,EAChE,KAAK,OAASmC,EACd,KAAM,CAACC,EAAKC,CAAM,EAAI,KAAK,SAAQ,EACnCF,EAAO,YAAYC,CAAG,EACtB,KAAK,UAAYA,EACjB,KAAK,gBAAkBC,EAAO,cAAc,SAAS,EACrD,KAAK,QAAUA,EAAO,cAAc,UAAU,EAC9C,KAAK,cAAgBA,EAAO,cAAc,WAAW,EACrD,KAAK,gBAAkBA,EAAO,cAAc,WAAW,EACvD,KAAK,OAASA,EAAO,cAAc,SAAS,EACxCH,GACAG,EAAO,YAAYH,CAAY,EAEnC,KAAK,WAAU,CAClB,CACD,2BAA2BI,EAAW,CAClC,IAAIH,EAOJ,GANI,OAAOG,GAAc,SACrBH,EAAS,SAAS,cAAcG,CAAS,EAEpCA,aAAqB,cAC1BH,EAASG,GAET,CAACH,EACD,MAAM,IAAI,MAAM,qBAAqB,EAEzC,OAAOA,CACV,CACD,YAAa,CACT,MAAMI,EAAoB3E,GAAM,CAC5B,MAAMZ,EAAO,KAAK,QAAQ,sBAAqB,EACzC2E,EAAI/D,EAAE,QAAUZ,EAAK,KACrB4E,EAAIhE,EAAE,QAAUZ,EAAK,KACrBwF,EAAYb,EAAI3E,EAAK,MACrByF,EAAYb,EAAI5E,EAAK,OAC3B,MAAO,CAACwF,EAAWC,CAAS,CACxC,EAEQ,KAAK,QAAQ,iBAAiB,QAAU7E,GAAM,CAC1C,KAAM,CAAC+D,EAAGC,CAAC,EAAIW,EAAiB3E,CAAC,EACjC,KAAK,KAAK,QAAS+D,EAAGC,CAAC,CACnC,CAAS,EAED,KAAK,QAAQ,iBAAiB,WAAahE,GAAM,CAC7C,KAAM,CAAC+D,EAAGC,CAAC,EAAIW,EAAiB3E,CAAC,EACjC,KAAK,KAAK,WAAY+D,EAAGC,CAAC,CACtC,CAAS,EAEG,KAAK,QAAQ,YACb,KAAK,SAAQ,EAGjB,KAAK,gBAAgB,iBAAiB,SAAU,IAAM,CAClD,KAAM,CAAE,WAAAc,EAAY,YAAAC,EAAa,YAAAC,CAAW,EAAK,KAAK,gBAChDrB,EAASmB,EAAaC,EACtBE,GAAQH,EAAaE,GAAeD,EAC1C,KAAK,KAAK,SAAUpB,EAAQsB,CAAI,CAC5C,CAAS,EAED,MAAMC,EAAQ,KAAK,YAAY,GAAG,EAClC,KAAK,eAAiB,IAAI,eAAe,IAAM,CAC3CA,EAAM,IAAM,KAAK,SAAQ,CAAE,CACvC,CAAS,EACD,KAAK,eAAe,QAAQ,KAAK,eAAe,CACnD,CACD,UAAW,CACP9B,GAAc,KAAK,QAEnB,CAAC+B,EAAGC,EAAIrB,IAAM,CACV,KAAK,KAAK,OAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,EAAI,KAAK,QAAQ,sBAAqB,EAAG,KAAK,CAAC,CAAC,CAC7F,EAED,IAAO,KAAK,WAAa,GAEzB,IAAO,KAAK,WAAa,EAAM,CAClC,CACD,WAAY,CAER,OAAI,KAAK,QAAQ,QAAU,KAChB,IACN,MAAM,OAAO,KAAK,QAAQ,MAAM,CAAC,EAElC,KAAK,QAAQ,SAAW,QACjB,KAAK,OAAO,cAAgB,IAF5B,OAAO,KAAK,QAAQ,MAAM,CAIxC,CACD,UAAW,CACP,MAAMS,EAAM,SAAS,cAAc,KAAK,EAClCC,EAASD,EAAI,aAAa,CAAE,KAAM,MAAM,CAAE,EAChD,OAAAC,EAAO,UAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBA8BH,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2CzB,CAACD,EAAKC,CAAM,CACtB,CAED,WAAWrC,EAAS,CAChB,GAAI,KAAK,QAAQ,YAAcA,EAAQ,UAAW,CAC9C,MAAMiD,EAAY,KAAK,2BAA2BjD,EAAQ,SAAS,EACnEiD,EAAU,YAAY,KAAK,SAAS,EACpC,KAAK,OAASA,CACjB,CACGjD,EAAQ,YAAc,CAAC,KAAK,QAAQ,YACpC,KAAK,SAAQ,EAEjB,KAAK,QAAUA,EAEf,KAAK,SAAQ,CAChB,CACD,YAAa,CACT,OAAO,KAAK,OACf,CACD,WAAY,CACR,OAAO,KAAK,gBAAgB,UAC/B,CACD,SAAU,CACN,IAAIb,EACJ,KAAK,UAAU,UACdA,EAAK,KAAK,kBAAoB,MAAQA,IAAO,QAAkBA,EAAG,WAAU,CAChF,CACD,YAAY+D,EAAU,GAAI,CACtB,MAAMC,EAAU,CAAA,EAChB,YAAK,SAAS,KAAKA,CAAO,EAClB9C,GAAa,CACjB8C,EAAQ,SAAW,aAAaA,EAAQ,OAAO,EAC/CA,EAAQ,QAAU,WAAW9C,EAAU6C,CAAO,CAC1D,CACK,CAED,mBAAmBE,EAAO,CACtB,GAAI,CAAC,MAAM,QAAQA,CAAK,EACpB,OAAOA,GAAS,GACpB,GAAIA,EAAM,OAAS,EACf,OAAOA,EAAM,CAAC,GAAK,GACvB,MAAMC,EAAgB,SAAS,cAAc,QAAQ,EAE/CC,EADMD,EAAc,WAAW,IAAI,EACpB,qBAAqB,EAAG,EAAG,EAAGA,EAAc,MAAM,EACjEE,EAAsB,GAAKH,EAAM,OAAS,GAChD,OAAAA,EAAM,QAAQ,CAACA,EAAOI,IAAU,CAC5B,MAAMC,EAASD,EAAQD,EACvBD,EAAS,aAAaG,EAAQL,CAAK,CAC/C,CAAS,EACME,CACV,CACD,kBAAkBlF,EAAa4B,EAAS0D,EAAKC,EAAQ,CACjD,MAAMC,EAAaxF,EAAY,CAAC,EAC1ByF,EAAgBzF,EAAY,CAAC,GAAKA,EAAY,CAAC,EAC/CG,EAASqF,EAAW,OACpB,CAAE,MAAAE,EAAO,OAAAC,GAAWL,EAAI,OACxBM,EAAaD,EAAS,EACtBE,EAAa,OAAO,kBAAoB,EACxCC,EAAWlE,EAAQ,SAAWA,EAAQ,SAAWiE,EAAa,EAC9DE,EAASnE,EAAQ,OAASA,EAAQ,OAASiE,EAAajE,EAAQ,SAAWkE,EAAW,EAAI,EAC1FE,EAAYpE,EAAQ,WAAa,EACjCqE,EAAgBP,GAASI,EAAWC,GAAU5F,EAC9C+F,EAASF,GAAa,cAAeV,EAAM,YAAc,OAC/DA,EAAI,UAAS,EACb,IAAIa,EAAQ,EACRC,EAAS,EACTC,EAAY,EAChB,QAAShG,EAAI,EAAGA,GAAKF,EAAQE,IAAK,CAC9B,MAAMkD,EAAI,KAAK,MAAMlD,EAAI4F,CAAa,EACtC,GAAI1C,EAAI4C,EAAO,CACX,MAAMG,EAAe,KAAK,MAAMF,EAASR,EAAaL,CAAM,EACtDgB,EAAkB,KAAK,MAAMF,EAAYT,EAAaL,CAAM,EAC5DiB,EAAYF,EAAeC,GAAmB,EAEpD,IAAI/C,EAAIoC,EAAaU,EACjB1E,EAAQ,WAAa,MACrB4B,EAAI,EAEC5B,EAAQ,WAAa,WAC1B4B,EAAImC,EAASa,GAEjBlB,EAAIY,CAAM,EAAEC,GAASL,EAAWC,GAASvC,EAAGsC,EAAUU,EAAWR,CAAS,EAC1EG,EAAQ5C,EACR6C,EAAS,EACTC,EAAY,CACf,CACD,MAAMI,EAAe,KAAK,IAAIjB,EAAWnF,CAAC,GAAK,CAAC,EAC1CqG,EAAkB,KAAK,IAAIjB,EAAcpF,CAAC,GAAK,CAAC,EAClDoG,EAAeL,IACfA,EAASK,GACTC,EAAkBL,IAClBA,EAAYK,EACnB,CACDpB,EAAI,KAAI,EACRA,EAAI,UAAS,CAChB,CACD,mBAAmBtF,EAAa2G,EAAUrB,EAAKC,EAAQ,CACnD,MAAMqB,EAAexB,GAAU,CAC3B,MAAM7E,EAAUP,EAAYoF,CAAK,GAAKpF,EAAY,CAAC,EAC7CG,EAASI,EAAQ,OACjB,CAAE,OAAAoF,CAAM,EAAKL,EAAI,OACjBM,EAAaD,EAAS,EACtBkB,EAASvB,EAAI,OAAO,MAAQnF,EAClCmF,EAAI,OAAO,EAAGM,CAAU,EACxB,IAAIO,EAAQ,EACR/F,EAAM,EACV,QAASC,EAAI,EAAGA,GAAKF,EAAQE,IAAK,CAC9B,MAAMkD,EAAI,KAAK,MAAMlD,EAAIwG,CAAM,EAC/B,GAAItD,EAAI4C,EAAO,CACX,MAAMW,EAAI,KAAK,MAAM1G,EAAMwF,EAAaL,CAAM,GAAK,EAC7C/B,EAAIoC,EAAakB,GAAK1B,IAAU,EAAI,GAAK,GAC/CE,EAAI,OAAOa,EAAO3C,CAAC,EACnB2C,EAAQ5C,EACRnD,EAAM,CACT,CACD,MAAMjB,EAAQ,KAAK,IAAIoB,EAAQF,CAAC,GAAK,CAAC,EAClClB,EAAQiB,IACRA,EAAMjB,EACb,CACDmG,EAAI,OAAOa,EAAOP,CAAU,CACxC,EACQN,EAAI,UAAS,EACbsB,EAAY,CAAC,EACbA,EAAY,CAAC,EACbtB,EAAI,KAAI,EACRA,EAAI,UAAS,CAChB,CACD,eAAetF,EAAa4B,EAAS0D,EAAK,CAGtC,GAFAA,EAAI,UAAY,KAAK,mBAAmB1D,EAAQ,SAAS,EAErDA,EAAQ,eAAgB,CACxBA,EAAQ,eAAe5B,EAAasF,CAAG,EACvC,MACH,CAED,IAAIC,EAAS3D,EAAQ,WAAa,EAClC,GAAIA,EAAQ,UAAW,CACnB,MAAMxB,EAAM,MAAM,KAAKJ,EAAY,CAAC,CAAC,EAAE,OAAO,CAACI,EAAKjB,IAAU,KAAK,IAAIiB,EAAK,KAAK,IAAIjB,CAAK,CAAC,EAAG,CAAC,EAC/FoG,EAASnF,EAAM,EAAIA,EAAM,CAC5B,CAED,GAAIwB,EAAQ,UAAYA,EAAQ,QAAUA,EAAQ,SAAU,CACxD,KAAK,kBAAkB5B,EAAa4B,EAAS0D,EAAKC,CAAM,EACxD,MACH,CAED,KAAK,mBAAmBvF,EAAa4B,EAAS0D,EAAKC,CAAM,CAC5D,CACD,mBAAmBvF,EAAa4B,EAAS8D,EAAOC,EAAQoB,EAAOC,EAAKC,EAAiBC,EAAmB,CACpG,MAAMrB,EAAa,OAAO,kBAAoB,EACxCsB,EAAS,SAAS,cAAc,QAAQ,EACxChH,EAASH,EAAY,CAAC,EAAE,OAC9BmH,EAAO,MAAQ,KAAK,MAAOzB,GAASsB,EAAMD,GAAU5G,CAAM,EAC1DgH,EAAO,OAASxB,EAASE,EACzBsB,EAAO,MAAM,MAAQ,GAAG,KAAK,MAAMA,EAAO,MAAQtB,CAAU,CAAC,KAC7DsB,EAAO,MAAM,OAAS,GAAGxB,CAAM,KAC/BwB,EAAO,MAAM,KAAO,GAAG,KAAK,MAAOJ,EAAQrB,EAASG,EAAa1F,CAAM,CAAC,KACxE8G,EAAgB,YAAYE,CAAM,EAClC,MAAM7B,EAAM6B,EAAO,WAAW,IAAI,EAGlC,GAFA,KAAK,eAAenH,EAAY,IAAKO,GAAYA,EAAQ,MAAMwG,EAAOC,CAAG,CAAC,EAAGpF,EAAS0D,CAAG,EAErF6B,EAAO,MAAQ,GAAKA,EAAO,OAAS,EAAG,CACvC,MAAMC,EAAiBD,EAAO,YACxBE,EAAcD,EAAe,WAAW,IAAI,EAClDC,EAAY,UAAUF,EAAQ,EAAG,CAAC,EAElCE,EAAY,yBAA2B,YACvCA,EAAY,UAAY,KAAK,mBAAmBzF,EAAQ,aAAa,EAErEyF,EAAY,SAAS,EAAG,EAAGF,EAAO,MAAOA,EAAO,MAAM,EACtDD,EAAkB,YAAYE,CAAc,CAC/C,CACJ,CACD,cAAcpH,EAAa4B,EAAS8D,EAAO,CAEvC,MAAMuB,EAAkB,SAAS,cAAc,KAAK,EAC9CtB,EAAS,KAAK,YACpBsB,EAAgB,MAAM,OAAS,GAAGtB,CAAM,KACxC,KAAK,cAAc,MAAM,UAAY,GAAGA,CAAM,KAC9C,KAAK,cAAc,YAAYsB,CAAe,EAE9C,MAAMC,EAAoBD,EAAgB,YAC1C,KAAK,gBAAgB,YAAYC,CAAiB,EAElD,KAAM,CAAE,WAAA5C,EAAY,YAAAC,EAAa,YAAAC,CAAW,EAAK,KAAK,gBAChD8C,EAAMtH,EAAY,CAAC,EAAE,OACrBuH,EAAQD,EAAM/C,EACpB,IAAIiD,EAAgB,KAAK,IAAI3D,GAAS,iBAAkBW,CAAW,EAEnE,GAAI5C,EAAQ,UAAYA,EAAQ,OAAQ,CACpC,MAAMkE,EAAWlE,EAAQ,UAAY,GAC/BmE,EAASnE,EAAQ,QAAUkE,EAAW,EACtC2B,EAAgB3B,EAAWC,EAC7ByB,EAAgBC,IAAkB,IAClCD,EAAgB,KAAK,MAAMA,EAAgBC,CAAa,EAAIA,EAEnE,CACD,MAAMV,EAAQ,KAAK,MAAM,KAAK,IAAIzC,CAAU,EAAIiD,CAAK,EAC/CP,EAAM,KAAK,MAAMD,EAAQS,EAAgBD,CAAK,EAC9CG,EAAcV,EAAMD,EAEpBY,EAAO,CAACZ,EAAOC,IAAQ,CACzB,KAAK,mBAAmBhH,EAAa4B,EAAS8D,EAAOC,EAAQ,KAAK,IAAI,EAAGoB,CAAK,EAAG,KAAK,IAAIC,EAAKM,CAAG,EAAGL,EAAiBC,CAAiB,CACnJ,EAEcU,EAAY,KAAK,cACjBC,EAAY,KAAK,cACjBC,EAAa,CAACC,EAAWC,IAAY,CACvCL,EAAKI,EAAWC,CAAO,EACnBD,EAAY,GACZH,EAAU,IAAM,CACZE,EAAWC,EAAYL,EAAaM,EAAUN,CAAW,CAC7E,CAAiB,CAEjB,EACcO,EAAa,CAACF,EAAWC,IAAY,CACvCL,EAAKI,EAAWC,CAAO,EACnBA,EAAUV,GACVO,EAAU,IAAM,CACZI,EAAWF,EAAYL,EAAaM,EAAUN,CAAW,CAC7E,CAAiB,CAEjB,EACQI,EAAWf,EAAOC,CAAG,EACjBA,EAAMM,GACNW,EAAWjB,EAAKA,EAAMU,CAAW,CAExC,CACD,OAAO9H,EAAW,CAEd,KAAK,SAAS,QAASmF,GAAYA,EAAQ,SAAW,aAAaA,EAAQ,OAAO,CAAC,EACnF,KAAK,SAAW,GAEhB,KAAK,cAAc,UAAY,GAC/B,KAAK,gBAAgB,UAAY,GACjC,KAAK,QAAQ,MAAM,MAAQ,GAEvB,KAAK,QAAQ,OAAS,OACtB,KAAK,gBAAgB,MAAM,MACvB,OAAO,KAAK,QAAQ,OAAU,SAAW,GAAG,KAAK,QAAQ,KAAK,KAAO,KAAK,QAAQ,OAG1F,MAAMc,EAAa,OAAO,kBAAoB,EACxCqC,EAAc,KAAK,gBAAgB,YACnC3D,EAAc,KAAK,KAAK3E,EAAU,UAAY,KAAK,QAAQ,aAAe,EAAE,EAElF,KAAK,YAAc2E,EAAc2D,EACjC,MAAMC,EAAiB,KAAK,QAAQ,YAAc,CAAC,KAAK,YAElDzC,GAASyC,EAAiBD,EAAc3D,GAAesB,EAS7D,GAPA,KAAK,QAAQ,MAAM,MAAQsC,EAAiB,OAAS,GAAG5D,CAAW,KAEnE,KAAK,gBAAgB,MAAM,UAAY,KAAK,YAAc,OAAS,SACnE,KAAK,gBAAgB,UAAU,OAAO,cAAe,CAAC,CAAC,KAAK,QAAQ,aAAa,EACjF,KAAK,OAAO,MAAM,gBAAkB,GAAG,KAAK,QAAQ,aAAe,KAAK,QAAQ,aAAa,GAC7F,KAAK,OAAO,MAAM,MAAQ,GAAG,KAAK,QAAQ,WAAW,KAEjD,KAAK,QAAQ,cAEb,QAASlE,EAAI,EAAGA,EAAIT,EAAU,iBAAkBS,IAAK,CACjD,MAAMuB,EAAU,OAAO,OAAO,OAAO,OAAO,CAAE,EAAE,KAAK,OAAO,EAAG,KAAK,QAAQ,cAAcvB,CAAC,CAAC,EAC5F,KAAK,cAAc,CAACT,EAAU,eAAeS,CAAC,CAAC,EAAGuB,EAAS8D,CAAK,CACnE,KAEA,CAED,MAAM0C,EAAW,CAACxI,EAAU,eAAe,CAAC,CAAC,EACzCA,EAAU,iBAAmB,GAC7BwI,EAAS,KAAKxI,EAAU,eAAe,CAAC,CAAC,EAC7C,KAAK,cAAcwI,EAAU,KAAK,QAAS1C,CAAK,CACnD,CACD,KAAK,UAAY9F,EACjB,KAAK,KAAK,QAAQ,CACrB,CACD,UAAW,CAEP,GAAI,CAAC,KAAK,UACN,OAEJ,MAAMyI,EAAoB,KAAK,gBAAgB,YAE/C,KAAK,OAAO,KAAK,SAAS,EAE1B,MAAMC,EAAqB,KAAK,gBAAgB,YAChD,KAAK,gBAAgB,YAAcA,EAAqBD,CAC3D,CACD,KAAKE,EAAa,CACd,KAAK,QAAQ,YAAcA,EAC3B,KAAK,SAAQ,CAChB,CACD,eAAeC,EAAUC,EAAY,GAAO,CACxC,KAAM,CAAE,YAAAjE,EAAa,WAAAF,EAAY,YAAAC,CAAW,EAAK,KAAK,gBAChDmE,EAAgBnE,EAAciE,EAC9BG,EAASnE,EAAc,EACvBoE,EAAYH,GAAa,KAAK,QAAQ,YAAc,CAAC,KAAK,WAAaE,EAASnE,EACtF,GAAIkE,EAAgBpE,EAAasE,GAAaF,EAAgBpE,EAE1D,GAAI,KAAK,QAAQ,YAAc,CAAC,KAAK,WAAY,CAE7C,MAAMuE,EAAUF,EAAS,GACrBD,GAAiBpE,EAAaqE,IAAWE,GAAWH,EAAgBpE,EAAaE,EACjF,KAAK,gBAAgB,YAAcqE,EAInC,KAAK,gBAAgB,WAAaH,EAAgBC,CAEzD,MACQ,KAAK,WAGV,KAAK,gBAAgB,WACjBD,EAAgBpE,EAAaoE,EAAgB,GAAMA,EAAgBlE,EAAc,GAIrF,KAAK,gBAAgB,WAAakE,EAI1C,CACI,KAAM,CAAE,WAAApE,CAAU,EAAK,KAAK,gBACtBnB,EAASmB,EAAaC,EACtBE,GAAQH,EAAaE,GAAeD,EAC1C,KAAK,KAAK,SAAUpB,EAAQsB,CAAI,CACnC,CACJ,CACD,eAAe+D,EAAUC,EAAW,CAChC,GAAI,MAAMD,CAAQ,EACd,OACJ,MAAMM,EAAWN,EAAW,IAC5B,KAAK,cAAc,MAAM,SAAW,WAAWM,CAAQ,2BAA2BA,CAAQ,UAC1F,KAAK,gBAAgB,MAAM,MAAQ,GAAGA,CAAQ,IAC9C,KAAK,OAAO,MAAM,KAAO,GAAGA,CAAQ,IACpC,KAAK,OAAO,MAAM,WAAa,KAAK,MAAMA,CAAQ,IAAM,IAAM,IAAI,KAAK,QAAQ,WAAW,KAAO,GAC7F,KAAK,aAAe,KAAK,QAAQ,YACjC,KAAK,eAAeN,EAAUC,CAAS,CAE9C,CACL,CACA5E,GAAS,iBAAmB,ICjgB5B,MAAMkF,WAActH,EAAa,CAC7B,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,YAAc,IAAA,EACtB,CACD,OAAQ,CACJ,KAAK,YAAc,KAAK,GAAG,OAAQ,IAAM,CACrC,sBAAsB,IAAM,CACxB,KAAK,KAAK,MAAM,CAChC,CAAa,CACb,CAAS,EACD,KAAK,KAAK,MAAM,CACnB,CACD,MAAO,CACH,KAAK,YAAW,CACnB,CACD,SAAU,CACN,KAAK,YAAW,CACnB,CACL,CCpBA,IAAI5C,GAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,CAAA,GAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EAKA,MAAMiK,WAAuBvH,EAAa,CACtC,YAAYwH,EAAe,IAAI,aAAgB,CAC3C,QACA,KAAK,WAAa,KAClB,KAAK,SAAW,GAChB,KAAK,cAAgB,EACrB,KAAK,eAAiB,EACtB,KAAK,OAAS,GACd,KAAK,OAAS,KACd,KAAK,WAAa,GAClB,KAAK,OAAS,GACd,KAAK,YAAc,KACnB,KAAK,aAAeA,EACpB,KAAK,SAAW,KAAK,aAAa,WAAU,EAC5C,KAAK,SAAS,QAAQ,KAAK,aAAa,WAAW,CACtD,CACD,MAAO,CACH,OAAOpK,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAE5D,CAAS,CACJ,CACD,IAAI,KAAM,CACN,OAAO,KAAK,UACf,CACD,IAAI,IAAIM,EAAO,CACX,KAAK,WAAaA,EAClB,MAAMA,CAAK,EACN,KAAM8B,GAAaA,EAAS,YAAW,CAAE,EACzC,KAAMiI,GAAgB,KAAK,aAAa,gBAAgBA,CAAW,CAAC,EACpE,KAAMC,GAAgB,CACvB,KAAK,OAASA,EACd,KAAK,KAAK,gBAAgB,EAC1B,KAAK,KAAK,SAAS,EACf,KAAK,UACL,KAAK,KAAI,CACzB,CAAS,CACJ,CACD,OAAQ,CACJ,IAAIpI,EACC,KAAK,SAEV,KAAK,OAAS,IACbA,EAAK,KAAK,cAAgB,MAAQA,IAAO,QAAkBA,EAAG,WAAU,EACzE,KAAK,WAAa,KAAK,aAAa,mBAAkB,EACtD,KAAK,WAAW,OAAS,KAAK,OAC9B,KAAK,WAAW,QAAQ,KAAK,QAAQ,EACjC,KAAK,gBAAkB,KAAK,WAC5B,KAAK,eAAiB,GAE1B,KAAK,WAAW,MAAM,KAAK,aAAa,YAAa,KAAK,cAAc,EACxE,KAAK,cAAgB,KAAK,aAAa,YACvC,KAAK,WAAW,QAAU,IAAM,CACxB,KAAK,aAAe,KAAK,WACzB,KAAK,MAAK,EACV,KAAK,KAAK,OAAO,EAEjC,EACK,CACD,QAAS,CACL,IAAIA,EACA,KAAK,SAET,KAAK,OAAS,IACbA,EAAK,KAAK,cAAgB,MAAQA,IAAO,QAAkBA,EAAG,KAAI,EACnE,KAAK,gBAAkB,KAAK,aAAa,YAAc,KAAK,cAC/D,CACD,MAAO,CACH,OAAOlC,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,KAAK,MAAK,EACV,KAAK,KAAK,MAAM,CAC5B,CAAS,CACJ,CACD,OAAQ,CACJ,KAAK,OAAM,EACX,KAAK,KAAK,OAAO,CACpB,CACD,UAAUuK,EAAU,CAChB,OAAOvK,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAEhD,OADW,KAAK,aACN,UAAUuK,CAAQ,CACxC,CAAS,CACJ,CACD,IAAI,cAAe,CACf,IAAIrI,EAAIC,EACR,OAAQA,GAAMD,EAAK,KAAK,cAAgB,MAAQA,IAAO,OAAS,OAASA,EAAG,aAAa,SAAW,MAAQC,IAAO,OAASA,EAAK,CACpI,CACD,IAAI,aAAa7B,EAAO,CAChB,KAAK,aACL,KAAK,WAAW,aAAa,MAAQA,EAE5C,CACD,IAAI,aAAc,CACd,OAAO,KAAK,OAAS,KAAK,eAAiB,KAAK,eAAiB,KAAK,aAAa,YAAc,KAAK,aACzG,CACD,IAAI,YAAYA,EAAO,CACnB,KAAK,KAAK,SAAS,EACf,KAAK,OACL,KAAK,eAAiBA,GAGtB,KAAK,OAAM,EACX,KAAK,eAAiBA,EACtB,KAAK,MAAK,GAEd,KAAK,KAAK,YAAY,CACzB,CACD,IAAI,UAAW,CACX,IAAI4B,EACJ,QAASA,EAAK,KAAK,UAAY,MAAQA,IAAO,OAAS,OAASA,EAAG,WAAa,CACnF,CACD,IAAI,QAAS,CACT,OAAO,KAAK,SAAS,KAAK,KAC7B,CACD,IAAI,OAAO5B,EAAO,CACd,KAAK,SAAS,KAAK,MAAQA,EAC3B,KAAK,KAAK,cAAc,CAC3B,CACD,IAAI,OAAQ,CACR,OAAO,KAAK,MACf,CACD,IAAI,MAAMA,EAAO,CACT,KAAK,SAAWA,IAEpB,KAAK,OAASA,EACV,KAAK,OACL,KAAK,SAAS,aAGd,KAAK,SAAS,QAAQ,KAAK,aAAa,WAAW,EAE1D,CAED,aAAc,CACV,OAAO,KAAK,QACf,CACL,CCpJA,IAAIN,GAAwC,SAAUC,EAASC,EAAYC,EAAGC,EAAW,CACrF,SAASC,EAAMC,EAAO,CAAE,OAAOA,aAAiBH,EAAIG,EAAQ,IAAIH,EAAE,SAAUI,EAAS,CAAEA,EAAQD,CAAK,CAAE,CAAE,CAAI,CAC5G,OAAO,IAAKH,IAAMA,EAAI,UAAU,SAAUI,EAASC,EAAQ,CACvD,SAASC,EAAUH,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,KAAKE,CAAK,CAAC,CAAE,OAAUK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC3F,SAASC,EAASN,EAAO,CAAE,GAAI,CAAEI,EAAKN,EAAU,MAASE,CAAK,CAAC,CAAI,OAAQK,EAAG,CAAEH,EAAOG,CAAC,EAAM,CAC9F,SAASD,EAAKG,EAAQ,CAAEA,EAAO,KAAON,EAAQM,EAAO,KAAK,EAAIR,EAAMQ,EAAO,KAAK,EAAE,KAAKJ,EAAWG,CAAQ,CAAI,CAC9GF,GAAMN,EAAYA,EAAU,MAAMH,EAASC,GAAc,CAAE,CAAA,GAAG,KAAI,CAAE,CAC5E,CAAK,CACL,EAOA,MAAMsK,GAAiB,CACnB,UAAW,OACX,cAAe,OACf,YAAa,EACb,YAAa,EACb,WAAY,GACZ,SAAU,GACV,WAAY,GACZ,WAAY,GACZ,WAAY,GACZ,WAAY,GAChB,EACA,MAAMC,WAAmBtH,EAAO,CAE5B,OAAO,OAAOJ,EAAS,CACnB,OAAO,IAAI0H,GAAW1H,CAAO,CAChC,CAED,YAAYA,EAAS,CACjB,MAAM2H,EAAQ3H,EAAQ,QACjBA,EAAQ,UAAY,WAAa,IAAIoH,GAAmB,QAC7D,MAAM,CACF,MAAAO,EACA,cAAe3H,EAAQ,cACvB,SAAUA,EAAQ,SAClB,aAAcA,EAAQ,SAClC,CAAS,EACD,KAAK,QAAU,GACf,KAAK,YAAc,KACnB,KAAK,cAAgB,GACrB,KAAK,mBAAqB,GAC1B,KAAK,QAAU,OAAO,OAAO,CAAA,EAAIyH,GAAgBzH,CAAO,EACxD,KAAK,MAAQ,IAAImH,GACjB,MAAMjF,EAAeyF,EAAQ,OAAY,KAAK,gBAAe,EAC7D,KAAK,SAAW,IAAI1F,GAAS,KAAK,QAASC,CAAY,EACvD,KAAK,iBAAgB,EACrB,KAAK,mBAAkB,EACvB,KAAK,gBAAe,EACpB,KAAK,YAAW,EAEhB,MAAMlD,EAAM,KAAK,QAAQ,KAAO,KAAK,SACjCA,EACA,KAAK,KAAKA,EAAK,KAAK,QAAQ,MAAO,KAAK,QAAQ,QAAQ,EAEnD,KAAK,QAAQ,OAAS,KAAK,QAAQ,UAExC,KAAK,eAAc,CAE1B,CACD,iBAAkB,CAEd,KAAK,cAAc,KAAK,KAAK,MAAM,GAAG,OAAQ,IAAM,CAChD,MAAM4I,EAAc,KAAK,iBACzB,KAAK,SAAS,eAAeA,EAAc,KAAK,YAAW,EAAI,EAAI,EACnE,KAAK,KAAK,aAAcA,CAAW,EACnC,KAAK,KAAK,eAAgBA,CAAW,CACxC,CAAA,CAAC,CACL,CACD,kBAAmB,CACf,KAAK,mBAAmB,KAAK,KAAK,aAAa,aAAc,IAAM,CAC/D,MAAMA,EAAc,KAAK,iBACzB,KAAK,SAAS,eAAeA,EAAc,KAAK,cAAe,KAAK,UAAS,CAAE,EAC/E,KAAK,KAAK,aAAcA,CAAW,CACtC,CAAA,EAAG,KAAK,aAAa,OAAQ,IAAM,CAChC,KAAK,KAAK,MAAM,EAChB,KAAK,MAAM,OACd,CAAA,EAAG,KAAK,aAAa,QAAS,IAAM,CACjC,KAAK,KAAK,OAAO,EACjB,KAAK,MAAM,MACd,CAAA,EAAG,KAAK,aAAa,UAAW,IAAM,CACnC,KAAK,MAAM,MACd,CAAA,EAAG,KAAK,aAAa,QAAS,IAAM,CACjC,KAAK,KAAK,QAAQ,CACrB,CAAA,EAAG,KAAK,aAAa,UAAW,IAAM,CACnC,KAAK,KAAK,UAAW,KAAK,eAAgB,CAAA,CAC7C,CAAA,CAAC,CACL,CACD,oBAAqB,CACjB,KAAK,cAAc,KAEnB,KAAK,SAAS,GAAG,QAAS,CAACpF,EAAWC,IAAc,CAC5C,KAAK,QAAQ,WACb,KAAK,OAAOD,CAAS,EACrB,KAAK,KAAK,cAAeA,EAAY,KAAK,YAAW,CAAE,EACvD,KAAK,KAAK,QAASA,EAAWC,CAAS,EAEvD,CAAS,EAED,KAAK,SAAS,GAAG,WAAY,CAACD,EAAWC,IAAc,CACnD,KAAK,KAAK,WAAYD,EAAWC,CAAS,CACtD,CAAS,EAED,KAAK,SAAS,GAAG,SAAU,CAAClB,EAAQsB,IAAS,CACzC,MAAMhE,EAAW,KAAK,cACtB,KAAK,KAAK,SAAU0C,EAAS1C,EAAUgE,EAAOhE,CAAQ,CAClE,CAAS,EAED,KAAK,SAAS,GAAG,SAAU,IAAM,CAC7B,KAAK,KAAK,QAAQ,CACrB,CAAA,CAAC,EAEF,CACI,IAAIgJ,EACJ,KAAK,cAAc,KAAK,KAAK,SAAS,GAAG,OAASrF,GAAc,CACvD,KAAK,QAAQ,WAGlB,KAAK,SAAS,eAAeA,CAAS,EAEtC,aAAaqF,CAAQ,EACrBA,EAAW,WAAW,IAAM,CACxB,KAAK,OAAOrF,CAAS,CACxB,EAAE,KAAK,UAAS,EAAK,EAAI,GAAG,EAC7B,KAAK,KAAK,cAAeA,EAAY,KAAK,YAAW,CAAE,EACvD,KAAK,KAAK,OAAQA,CAAS,EAC9B,CAAA,CAAC,CACL,CACJ,CACD,aAAc,CACV,IAAIrD,EACG,GAAAA,EAAK,KAAK,QAAQ,WAAa,MAAQA,IAAO,SAAkBA,EAAG,QAE1E,KAAK,QAAQ,QAAQ,QAAS2I,GAAW,CACrC,KAAK,eAAeA,CAAM,CACtC,CAAS,CACJ,CACD,yBAA0B,CACtB,KAAK,mBAAmB,QAASC,GAAgBA,EAAa,CAAA,EAC9D,KAAK,mBAAqB,EAC7B,CAED,WAAW/H,EAAS,CAChB,KAAK,QAAU,OAAO,OAAO,CAAE,EAAE,KAAK,QAASA,CAAO,EACtD,KAAK,SAAS,WAAW,KAAK,OAAO,EACjCA,EAAQ,WACR,KAAK,gBAAgBA,EAAQ,SAAS,EAEtCA,EAAQ,eAAiB,OACzB,KAAK,gBAAiB,EAAC,SAAWA,EAAQ,cAEjD,CAED,eAAe8H,EAAQ,CACnB,OAAAA,EAAO,KAAK,IAAI,EAChB,KAAK,QAAQ,KAAKA,CAAM,EAExB,KAAK,cAAc,KAAKA,EAAO,KAAK,UAAW,IAAM,CACjD,KAAK,QAAU,KAAK,QAAQ,OAAQE,GAAMA,IAAMF,CAAM,CACzD,CAAA,CAAC,EACKA,CACV,CAED,YAAa,CACT,OAAO,KAAK,SAAS,YACxB,CAED,WAAY,CACR,OAAO,KAAK,SAAS,WACxB,CAED,kBAAmB,CACf,OAAO,KAAK,OACf,CACD,gBAAiB,CACb,OAAO7K,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAC5C,KAAK,QAAQ,OAAS,KAAK,QAAQ,WACnC,KAAK,YAAc6B,GAAQ,aAAa,KAAK,QAAQ,MAAO,KAAK,QAAQ,QAAQ,EACjF,MAAM,QAAQ,UACd,KAAK,cAAa,EAElC,CAAS,CACJ,CACD,eAAgB,CACZ,OAAO7B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAC5C,KAAK,cACL,KAAK,KAAK,SAAU,KAAK,YAAa,CAAA,EACtC,KAAK,SAAS,OAAO,KAAK,WAAW,EAErD,CAAS,CACJ,CACD,UAAU+B,EAAKuB,EAAMnC,EAAaS,EAAU,CACxC,OAAO5B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAMhD,GALA,KAAK,KAAK,OAAQ+B,CAAG,EACjB,CAAC,KAAK,QAAQ,OAAS,KAAK,UAAW,GACvC,KAAK,MAAK,EACd,KAAK,YAAc,KAEf,CAACuB,GAAQ,CAACnC,EAAa,CACvB,MAAM6J,EAActI,GAAe,KAAK,KAAK,UAAWA,CAAU,EAClEY,EAAO,MAAMX,GAAQ,UAAUZ,EAAKiJ,EAAY,KAAK,QAAQ,WAAW,CAC3E,CAYD,GAVA,KAAK,OAAOjJ,EAAKuB,CAAI,EAGrB1B,GACK,MAAM,QAAQ,QAAQA,GAAY,KAAK,YAAW,CAAE,KAChD,MAAM,IAAI,QAASrB,GAAY,CAC5B,KAAK,eAAe,iBAAkB,IAAMA,EAAQ,KAAK,YAAa,CAAA,CAAC,CAC/F,CAAqB,KACA,MAAM,QAAQ,QAAQ,CAAC,GAE5BY,EACA,KAAK,YAAcU,GAAQ,aAAaV,EAAaS,CAAQ,UAExD0B,EAAM,CACX,MAAM+G,EAAc,MAAM/G,EAAK,cAC/B,KAAK,YAAc,MAAMzB,GAAQ,OAAOwI,EAAa,KAAK,QAAQ,UAAU,CAC/E,CACD,KAAK,cAAa,EAClB,KAAK,KAAK,QAAS,KAAK,YAAa,CAAA,CACjD,CAAS,CACJ,CAED,KAAKtI,EAAKZ,EAAaS,EAAU,CAC7B,OAAO5B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,MAAM,KAAK,UAAU+B,EAAK,OAAWZ,EAAaS,CAAQ,CACtE,CAAS,CACJ,CAED,SAAS0B,EAAMnC,EAAaS,EAAU,CAClC,OAAO5B,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,MAAM,KAAK,UAAU,OAAQsD,EAAMnC,EAAaS,CAAQ,CACpE,CAAS,CACJ,CAED,KAAK8H,EAAa,CACd,GAAI,CAAC,KAAK,YACN,MAAM,IAAI,MAAM,iBAAiB,EAErC,KAAK,SAAS,KAAKA,CAAW,EAC9B,KAAK,KAAK,OAAQA,CAAW,CAChC,CAED,gBAAiB,CACb,OAAO,KAAK,WACf,CAED,YAAY,CAAE,SAAAH,EAAW,EAAG,UAAA0B,EAAY,IAAM,UAAAC,EAAY,GAAO,EAAG,GAAI,CACpE,GAAI,CAAC,KAAK,YACN,MAAM,IAAI,MAAM,oCAAoC,EAExD,MAAMC,EAAc,KAAK,IAAI5B,EAAU,KAAK,YAAY,gBAAgB,EAClE6B,EAAQ,CAAA,EACd,QAAS5J,EAAI,EAAGA,EAAI2J,EAAa3J,IAAK,CAClC,MAAME,EAAU,KAAK,YAAY,eAAeF,CAAC,EAC3C6J,EAAO,CAAA,EACPC,EAAa,KAAK,MAAM5J,EAAQ,OAASuJ,CAAS,EACxD,QAASzJ,EAAI,EAAGA,EAAIyJ,EAAWzJ,IAAK,CAChC,MAAM+J,EAAS7J,EAAQ,MAAMF,EAAI8J,GAAa9J,EAAI,GAAK8J,CAAU,EAC3D/J,EAAM,KAAK,IAAI,GAAGgK,CAAM,EAC9BF,EAAK,KAAK,KAAK,MAAM9J,EAAM2J,CAAS,EAAIA,CAAS,CACpD,CACDE,EAAM,KAAKC,CAAI,CAClB,CACD,OAAOD,CACV,CAED,aAAc,CACV,IAAIxJ,EAAW,MAAM,YAAW,GAAM,EAEtC,OAAKA,IAAa,GAAKA,IAAa,MAAa,KAAK,cAClDA,EAAW,KAAK,YAAY,UAEzBA,CACV,CAED,kBAAkB4J,EAAe,CAC7B,KAAK,QAAQ,SAAWA,CAC3B,CAED,OAAO7B,EAAU,CACb,MAAMlG,EAAO,KAAK,YAAW,EAAKkG,EAClC,KAAK,QAAQlG,CAAI,CACpB,CAED,WAAY,CACR,OAAOzD,GAAU,KAAM,OAAQ,OAAQ,WAAa,CAChD,OAAO,KAAK,YAAc,KAAK,QAAU,KAAK,MAC1D,CAAS,CACJ,CAED,MAAO,CACH,KAAK,MAAK,EACV,KAAK,QAAQ,CAAC,CACjB,CAED,KAAKyL,EAAS,CACV,KAAK,QAAQ,KAAK,eAAgB,EAAGA,CAAO,CAC/C,CAED,OAAQ,CACJ,KAAK,KAAK,GAAI,CAAC,CAAC,CAAC,CAAC,EAAG,IAAK,CAC7B,CAED,gBAAgBjI,EAAS,CACrB,KAAK,wBAAuB,EAC5B,MAAM,gBAAgBA,CAAO,EAC7B,KAAK,iBAAgB,CACxB,CAED,SAAU,CACN,KAAK,KAAK,SAAS,EACnB,KAAK,QAAQ,QAASqH,GAAWA,EAAO,QAAO,CAAE,EACjD,KAAK,cAAc,QAASC,GAAgBA,EAAa,CAAA,EACzD,KAAK,wBAAuB,EAC5B,KAAK,MAAM,UACX,KAAK,SAAS,UACd,MAAM,QAAO,CAChB,CACL,CCrUO,SAASY,GAAiBpB,EAAsC,CACtE,MAAMqB,EAAYrB,EAAY,iBACxBhJ,EAASgJ,EAAY,OAASqB,EAAY,EAAI,GAC9CC,EAAS,IAAI,YAAYtK,CAAM,EAC/BuK,EAAO,IAAI,SAASD,CAAM,EAChC,IAAIpF,EAAS,EAGb,MAAMsF,EAAc,SACnBD,EACArF,EACAuF,EACO,CACP,QAASvK,EAAI,EAAGA,EAAIuK,EAAO,OAAQvK,IAClCqK,EAAK,SAASrF,EAAShF,EAAGuK,EAAO,WAAWvK,CAAC,CAAC,CAC/C,EAGWsK,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACVqF,EAAK,UAAUrF,EAAQlF,EAAS,EAAG,EAAI,EAC7BkF,GAAA,EACEsF,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACEsF,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACLqF,EAAA,UAAUrF,EAAQ,GAAI,EAAI,EACrBA,GAAA,EACLqF,EAAA,UAAUrF,EAAQ,EAAG,EAAI,EACpBA,GAAA,EACLqF,EAAA,UAAUrF,EAAQmF,EAAW,EAAI,EAC5BnF,GAAA,EACVqF,EAAK,UAAUrF,EAAQ8D,EAAY,WAAY,EAAI,EACzC9D,GAAA,EACVqF,EAAK,UAAUrF,EAAQ8D,EAAY,WAAa,EAAIqB,EAAW,EAAI,EACzDnF,GAAA,EACVqF,EAAK,UAAUrF,EAAQmF,EAAY,EAAG,EAAI,EAChCnF,GAAA,EACLqF,EAAA,UAAUrF,EAAQ,GAAI,EAAI,EACrBA,GAAA,EACEsF,EAAAD,EAAMrF,EAAQ,MAAM,EACtBA,GAAA,EACVqF,EAAK,UAAUrF,EAAQ8D,EAAY,OAASqB,EAAY,EAAG,EAAI,EACrDnF,GAAA,EAGV,QAAShF,EAAI,EAAGA,EAAI8I,EAAY,OAAQ9I,IACvC,QAASE,EAAU,EAAGA,EAAUiK,EAAWjK,IAAW,CACrD,MAAM6J,EAAS,KAAK,IACnB,GACA,KAAK,IAAI,EAAGjB,EAAY,eAAe5I,CAAO,EAAEF,CAAC,CAAC,CAAA,EAEnDqK,EAAK,SAASrF,EAAQ+E,EAAS,MAAQ,EAAI,EACjC/E,GAAA,CACX,CAGM,OAAA,IAAI,WAAWoF,CAAM,CAC7B,CC1CO,MAAMI,GAAgB,MAC5B1B,EACApC,EACAC,EACA8D,IACyB,CACnB,MAAA7B,EAAe,IAAI,aAAa,CACrC,WAAY6B,GAAwB3B,EAAY,UAAA,CAChD,EACK4B,EAAmB5B,EAAY,iBAC/BtJ,EAAaiL,GAAwB3B,EAAY,WAEvD,IAAI6B,EAAgB7B,EAAY,OAC5B8B,EAAc,EAEdlE,GAASC,IACEiE,EAAA,KAAK,MAAMlE,EAAQlH,CAAU,EAE3CmL,EADkB,KAAK,MAAMhE,EAAMnH,CAAU,EACjBoL,GAG7B,MAAMC,EAAqBjC,EAAa,aACvC8B,EACAC,EACAnL,CAAA,EAGD,QAASU,EAAU,EAAGA,EAAUwK,EAAkBxK,IAAW,CACtD,MAAAP,EAAcmJ,EAAY,eAAe5I,CAAO,EAChD4K,EAAcD,EAAmB,eAAe3K,CAAO,EAC7D,QAASF,EAAI,EAAGA,EAAI2K,EAAe3K,IAClC8K,EAAY9K,CAAC,EAAIL,EAAYiL,EAAc5K,CAAC,CAE9C,CAEA,OAAOkK,GAAiBW,CAAkB,CAC3C,EAaaE,GAAa,CAACC,EAAsBC,IAAyB,CACpED,GACLA,EAAS,KAAKC,CAAM,CACrB,EAEaC,GAAyB,CACrCC,EACAC,KAEKA,IACUA,EAAA,GAEPD,EAAiB,IAAOC,GAAe,GC7EhD,MAAMC,EAAC,CAAC,aAAa,CAAC,KAAK,UAAU,CAAA,EAAG,KAAK,GAAG,KAAK,iBAAiB,KAAK,GAAG,KAAK,mBAAmB,CAAC,iBAAiBA,EAAElM,EAAE,EAAE,CAAC,GAAG,KAAK,UAAUkM,CAAC,IAAI,KAAK,UAAUA,CAAC,EAAE,IAAI,KAAK,KAAK,UAAUA,CAAC,EAAE,IAAIlM,CAAC,EAAiB,GAAE,KAAK,CAAC,MAAMa,EAAE,IAAI,CAAC,KAAK,oBAAoBqL,EAAErL,CAAC,EAAE,KAAK,oBAAoBqL,EAAElM,CAAC,CAAC,EAAE,OAAO,KAAK,iBAAiBkM,EAAErL,CAAC,EAAEA,CAAC,CAAC,MAAM,IAAI,KAAK,oBAAoBqL,EAAElM,CAAC,CAAC,CAAC,oBAAoBkM,EAAElM,EAAE,CAAC,IAAI,GAAU,EAAE,KAAK,UAAUkM,CAAC,KAA1B,MAAuC,IAAT,QAAY,EAAE,OAAOlM,CAAC,CAAC,CAAC,KAAKkM,EAAElM,EAAE,CAAC,OAAO,KAAK,GAAGkM,EAAElM,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,UAAU,EAAE,CAAC,KAAKkM,KAAKlM,EAAE,CAAC,KAAK,UAAUkM,CAAC,GAAG,KAAK,UAAUA,CAAC,EAAE,QAASA,GAAGA,EAAE,GAAGlM,CAAC,CAAG,CAAA,CAAC,CAAC,MAAMA,WAAUkM,EAAC,CAAC,YAAYA,EAAE,CAAC,QAAQ,KAAK,cAAc,CAAE,EAAC,KAAK,QAAQA,CAAC,CAAC,QAAQ,EAAE,KAAKA,EAAE,CAAC,KAAK,WAAWA,EAAE,KAAK,OAAQ,CAAA,CAAC,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE,KAAK,cAAc,QAASA,GAAGA,EAAC,EAAI,CAAC,CAAC,SAASrL,GAAEqL,EAAE,EAAErL,EAAEH,EAAEyL,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAE,EAAC,GAAG,CAACD,EAAE,OAAO,EAAE,MAAM,EAAEE,GAAG,CAAC,GAAOA,EAAE,SAAN,EAAa,OAAOA,EAAE,eAAc,EAAGA,EAAE,gBAAe,EAAGF,EAAE,MAAM,YAAY,OAAO,IAAI,EAAEE,EAAE,QAAQ9E,EAAE8E,EAAE,QAAQ,EAAE,GAAG,MAAMC,EAAE3L,GAAG,CAACA,EAAE,eAAgB,EAACA,EAAE,gBAAiB,EAAC,MAAM4L,EAAE5L,EAAE,QAAQ0L,EAAE1L,EAAE,QAAQ,GAAG,GAAG,KAAK,IAAI4L,EAAE,CAAC,GAAGH,GAAG,KAAK,IAAIC,EAAE9E,CAAC,GAAG6E,EAAE,CAAC,KAAK,CAAC,KAAKzL,EAAE,IAAIyL,CAAC,EAAED,EAAE,wBAAwB,IAAI,EAAE,GAAYrL,IAAE,EAAEH,EAAE4G,EAAE6E,CAAC,GAAG,EAAEG,EAAE,EAAEF,EAAE9E,EAAEgF,EAAE5L,EAAE0L,EAAED,CAAC,EAAE,EAAEG,EAAEhF,EAAE8E,CAAC,CAAC,EAAEG,EAAEL,GAAG,CAAC,IAAIA,EAAE,eAAgB,EAACA,EAAE,kBAAkB,EAAEM,EAAE,IAAI,CAACN,EAAE,MAAM,YAAY,GAAG,GAAaxL,IAAG,EAAE,EAAC,CAAE,EAAE,SAAS,iBAAiB,cAAc2L,CAAC,EAAE,SAAS,iBAAiB,YAAYG,CAAC,EAAE,SAAS,iBAAiB,eAAeA,CAAC,EAAE,SAAS,iBAAiB,QAAQD,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,oBAAoB,cAAcF,CAAC,EAAE,SAAS,oBAAoB,YAAYG,CAAC,EAAE,SAAS,oBAAoB,eAAeA,CAAC,EAAE,WAAY,IAAI,CAAC,SAAS,oBAAoB,QAAQD,EAAE,EAAE,CAAC,EAAG,EAAE,CAAC,CAAC,EAAE,OAAOL,EAAE,iBAAiB,cAAc,CAAC,EAAE,IAAI,CAAC,EAAG,EAACA,EAAE,oBAAoB,cAAc,CAAC,CAAC,CAAC,CAAC,MAAMxL,WAAUwL,EAAC,CAAC,YAAYA,EAAElM,EAAE,EAAE,EAAE,CAAC,IAAI,EAAEmM,EAAEG,EAAEF,EAAE,EAAE9E,EAAE,EAAE,QAAQ,KAAK,cAActH,EAAE,KAAK,iBAAiB,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU,IAAI,KAAK,GAAGkM,EAAE,IAAI,UAAU,KAAK,OAAQ,EAAC,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,cAAcA,EAAE,KAAK,EAAE,KAAK,IAAI,KAAK,eAAsB,EAAEA,EAAE,OAAZ,MAA2B,IAAT,OAAW,EAAEA,EAAE,KAAK,EAAE,KAAK,MAAaC,EAAED,EAAE,QAAZ,MAA4BC,IAAT,QAAYA,EAAE,KAAK,QAAeG,EAAEJ,EAAE,UAAZ,MAA8BI,IAAT,QAAYA,EAAE,KAAK,OAAcF,EAAEF,EAAE,SAAZ,MAA6BE,IAAT,OAAWA,EAAE,qBAAqB,KAAK,WAAkB,EAAEF,EAAE,aAAZ,MAAiC,IAAT,OAAW,EAAE,KAAK,UAAU,KAAK,WAAkB5E,EAAE4E,EAAE,aAAZ,MAAiC5E,IAAT,OAAWA,EAAE,KAAK,UAAU,KAAK,YAAmB,EAAE4E,EAAE,cAAZ,MAAkC,IAAT,OAAW,EAAE,GAAG,KAAK,QAAQ,KAAK,YAAW,EAAG,KAAK,WAAWA,EAAE,OAAO,EAAE,KAAK,QAAS,EAAC,KAAK,eAAgB,EAAC,KAAK,gBAAiB,CAAA,CAAC,cAAcA,EAAE,CAAC,OAAO,KAAK,IAAI,EAAE,KAAK,IAAI,KAAK,cAAcA,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAMA,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,aAAa,OAAO,GAAGA,EAAE,SAAS,QAAQ,IAAI,KAAK,EAAE,EAAE,CAAC,CAAC,iBAAiBA,EAAE,CAAC,MAAMlM,EAAE,SAAS,cAAc,KAAK,EAAEA,EAAE,aAAa,cAAc,MAAM,EAAEA,EAAE,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAA+R,EAAEA,EAAE,aAAa,OAAO,kCAAkC,EAAE,MAAMU,EAAEV,EAAE,UAAW,EAACU,EAAE,aAAa,cAAc,OAAO,EAAEA,EAAE,MAAM,KAAK,GAAGA,EAAE,MAAM,MAAM,IAAIA,EAAE,MAAM,YAAYA,EAAE,MAAM,WAAWA,EAAE,MAAM,WAAW,GAAGA,EAAE,MAAM,aAAa,cAAcA,EAAE,aAAa,OAAO,mCAAmC,EAAEwL,EAAE,YAAYlM,CAAC,EAAEkM,EAAE,YAAYxL,CAAC,EAAEG,GAAEb,EAAGkM,GAAG,KAAK,SAASA,EAAE,OAAO,EAAI,IAAI,KAAO,IAAI,KAAK,cAAe,EAAE,CAAC,EAAErL,GAAEH,EAAGwL,GAAG,KAAK,SAASA,EAAE,KAAK,EAAI,IAAI,KAAO,IAAI,KAAK,cAAa,EAAI,CAAC,CAAC,CAAC,oBAAoBA,EAAE,CAAC,MAAMlM,EAAEkM,EAAE,cAAc,sBAAsB,EAAE,EAAEA,EAAE,cAAc,uBAAuB,EAAElM,GAAGkM,EAAE,YAAYlM,CAAC,EAAE,GAAGkM,EAAE,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,MAAMA,EAAE,SAAS,cAAc,KAAK,EAAElM,EAAE,KAAK,QAAQ,KAAK,IAAI,IAAI,EAAE,EAAE,EAAE,IAAI,OAAO,KAAK,YAAY,GAAG,KAAK,WAAW,KAAK,mBAAmB,EAAE,IAAI,KAAK,iBAAiB,EAAE,EAAE,KAAK,YAAYkM,EAAE,aAAa,QAAQ;AAAA;AAAA,aAA2C,CAAC;AAAA,gBAAqB,CAAC;AAAA,0BAA+BlM,EAAE,OAAO,KAAK,KAAK;AAAA,qBAAyBA,EAAE,aAAa,KAAK,MAAM,MAAM;AAAA;AAAA;AAAA;AAAA,gBAA6H,KAAK,KAAK,OAAO,SAAS;AAAA;AAAA,KAAqC,EAAE,CAACA,GAAG,KAAK,QAAQ,KAAK,iBAAiBkM,CAAC,EAAEA,CAAC,CAAC,gBAAgB,CAAC,MAAMA,EAAE,KAAK,MAAM,KAAK,cAAclM,GAAG,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,QAAQ,MAAM,KAAK,IAAIkM,EAAE,IAAI,KAAK,QAAQ,MAAM,MAAM,IAAIlM,EAAE,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQkM,CAAC,EAAE,KAAKA,IAAIA,EAAE,iBAAiB,QAAS,GAAG,KAAK,KAAK,QAAQ,CAAC,GAAIA,EAAE,iBAAiB,aAAc,GAAG,KAAK,KAAK,OAAO,CAAC,CAAG,EAACA,EAAE,iBAAiB,aAAc,GAAG,KAAK,KAAK,QAAQ,CAAC,CAAC,EAAGA,EAAE,iBAAiB,WAAY,GAAG,KAAK,KAAK,WAAW,CAAC,CAAG,EAACrL,GAAEqL,EAAG,GAAG,KAAK,OAAO,CAAC,EAAI,IAAI,KAAK,cAAe,EAAG,IAAI,KAAK,YAAa,CAAA,EAAG,CAAC,eAAe,CAAC,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO,WAAW,CAAC,aAAa,CAAC,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO,OAAO,KAAK,KAAK,YAAY,EAAE,CAAC,UAAUA,EAAElM,EAAE,CAAC,GAAG,CAAC,KAAK,QAAQ,cAAc,OAAO,MAAM,EAAEkM,EAAE,KAAK,QAAQ,cAAc,YAAY,KAAK,cAAc,EAAElM,GAAaA,IAAV,QAAY,KAAK,MAAM,KAAK,MAAM,EAAEmM,EAAEnM,GAAWA,IAAR,MAAU,KAAK,IAAI,KAAK,IAAI,EAAEsM,EAAEH,EAAE,EAAE,GAAG,GAAGA,GAAG,KAAK,eAAe,GAAGA,GAAGG,GAAG,KAAK,WAAWA,GAAG,KAAK,YAAY,KAAK,MAAM,EAAE,KAAK,IAAIH,EAAE,KAAK,eAAgB,EAAC,KAAK,KAAK,QAAQ,EAAE,CAAC,OAAOD,EAAE,CAAC,KAAK,MAAM,KAAK,UAAUA,CAAC,CAAC,CAAC,SAASA,EAAElM,EAAE,CAAC,KAAK,QAAQ,KAAK,UAAUkM,EAAElM,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,QAAQ,KAAK,KAAK,YAAY,CAAC,CAAC,kBAAkBkM,EAAE,CAAC,KAAK,cAAcA,EAAE,KAAK,gBAAgB,CAAC,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,WAAWA,EAAE,CAAC,IAAIlM,EAAE,IAAWA,EAAE,KAAK,WAAf,MAAkCA,IAAT,QAAYA,EAAE,OAAM,EAAGkM,EAAE,CAAC,GAAa,OAAOA,GAAjB,SAAmB,CAAC,KAAK,QAAQ,SAAS,cAAc,KAAK,EAAE,MAAMlM,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM,QAAQ,SAASA,EAAE,GAAG,EAAE,KAAK,KAAK,QAAQ,YAAYkM,CAAC,MAAM,KAAK,QAAQA,EAAE,KAAK,QAAQ,aAAa,OAAO,gBAAgB,EAAE,KAAK,QAAQ,YAAY,KAAK,OAAO,CAAC,MAAM,KAAK,QAAQ,MAAM,CAAC,WAAWA,EAAE,CAAC,IAAIlM,EAAE,EAAE,GAAGkM,EAAE,QAAQ,KAAK,MAAMA,EAAE,MAAM,KAAK,QAAQ,MAAM,gBAAgB,KAAK,OAAgBA,EAAE,OAAX,SAAkB,KAAK,KAAKA,EAAE,KAAK,KAAK,QAAQ,MAAM,OAAO,KAAK,KAAK,OAAO,WAAoBA,EAAE,QAAX,QAA2BA,EAAE,MAAX,OAAe,CAAC,MAAM,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,KAAK,eAAsBlM,EAAEkM,EAAE,SAAZ,MAA6BlM,IAAT,OAAWA,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,KAAK,eAAsB,EAAEkM,EAAE,OAAZ,MAA2B,IAAT,OAAW,EAAE,EAAE,KAAK,MAAM,KAAK,GAAG,EAAE,KAAK,eAAc,EAAG,KAAK,QAAS,CAAA,CAAC,GAAGA,EAAE,SAAS,KAAK,WAAWA,EAAE,OAAO,EAAEA,EAAE,KAAK,KAAK,GAAGA,EAAE,GAAG,KAAK,WAAoBA,EAAE,SAAX,QAAmBA,EAAE,SAAS,KAAK,OAAO,CAAC,MAAMlM,EAAE,KAAK,QAAQ,KAAK,IAAI,KAAK,OAAOkM,EAAE,OAAO,KAAK,QAAQ,CAAClM,EAAE,KAAK,iBAAiB,KAAK,OAAO,EAAE,KAAK,oBAAoB,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE,KAAK,QAAQ,OAAQ,EAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAMmM,WAAUnM,EAAC,CAAC,YAAYkM,EAAE,CAAC,MAAMA,CAAC,EAAE,KAAK,QAAQ,GAAG,KAAK,iBAAiB,KAAK,sBAAsB,CAAC,OAAO,OAAOA,EAAE,CAAC,OAAO,IAAIC,GAAED,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,WAAW,MAAM,MAAM,+BAA+B,EAAE,KAAK,WAAW,aAAa,YAAY,KAAK,gBAAgB,EAAE,IAAIA,EAAE,GAAG,KAAK,cAAc,KAAK,KAAK,WAAW,GAAG,aAAclM,GAAG,CAAC,MAAM,EAAE,KAAK,QAAQ,OAAQkM,GAAGA,EAAE,OAAOlM,GAAGkM,EAAE,KAAKlM,GAAI,EAAE,QAASA,GAAG,CAACkM,EAAE,SAASlM,CAAC,GAAG,KAAK,KAAK,YAAYA,CAAC,CAAC,GAAIkM,EAAE,QAASA,GAAG,CAAC,EAAE,SAASA,CAAC,GAAG,KAAK,KAAK,aAAaA,CAAC,CAAC,GAAIA,EAAE,CAAC,EAAG,CAAC,CAAC,sBAAsB,CAAC,MAAMA,EAAE,SAAS,cAAc,KAAK,EAAE,OAAOA,EAAE,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAA2J,EAAEA,CAAC,CAAC,YAAY,CAAC,OAAO,KAAK,OAAO,CAAC,iBAAiBA,EAAE,CAAC,GAAG,CAACA,EAAE,QAAQ,OAAO,MAAMlM,EAAEkM,EAAE,QAAQ,EAAElM,EAAE,wBAAwB,KAAK,EAAEkM,EAAE,QAAQ,YAAYC,EAAE,KAAK,QAAQ,OAAQnM,GAAG,CAAC,GAAGA,IAAIkM,GAAG,CAAClM,EAAE,QAAQ,MAAM,GAAG,MAAMmM,EAAEnM,EAAE,QAAQ,sBAAuB,EAAC,KAAKsM,EAAEtM,EAAE,QAAQ,YAAY,OAAO,EAAEmM,EAAEG,GAAGH,EAAE,EAAE,CAAC,CAAG,EAAC,IAAKD,GAAG,CAAC,IAAIlM,EAAE,QAAeA,EAAEkM,EAAE,WAAZ,MAA+BlM,IAAT,OAAW,OAAOA,EAAE,sBAAqB,EAAG,SAAS,CAAC,CAAG,EAAC,OAAQ,CAACkM,EAAElM,IAAIkM,EAAElM,EAAG,CAAC,EAAEA,EAAE,MAAM,UAAU,GAAGmM,CAAC,IAAI,CAAC,WAAWD,EAAE,CAAC,KAAK,iBAAiB,YAAYA,EAAE,OAAO,EAAE,KAAK,iBAAiBA,CAAC,EAAE,KAAK,QAAQ,KAAKA,CAAC,EAAE,MAAMlM,EAAE,CAACkM,EAAE,GAAG,aAAc,IAAI,CAAC,KAAK,iBAAiBA,CAAC,EAAE,KAAK,KAAK,iBAAiBA,CAAC,CAAC,CAAC,EAAGA,EAAE,GAAG,OAAQ,IAAI,CAAC,IAAIlM,EAAEa,GAAUb,EAAE,KAAK,cAAf,MAAqCA,IAAT,QAAYA,EAAE,KAAM,GAASa,EAAE,KAAK,cAAf,MAAqCA,IAAT,QAAYA,EAAE,QAAQqL,EAAE,KAAK,CAAC,CAAG,EAACA,EAAE,GAAG,QAASlM,GAAG,CAAC,KAAK,KAAK,iBAAiBkM,EAAElM,CAAC,CAAC,GAAIkM,EAAE,GAAG,WAAYlM,GAAG,CAAC,KAAK,KAAK,wBAAwBkM,EAAElM,CAAC,CAAC,GAAIkM,EAAE,KAAK,SAAU,IAAI,CAAClM,EAAE,QAASkM,GAAGA,EAAG,CAAA,EAAG,KAAK,QAAQ,KAAK,QAAQ,OAAQlM,GAAGA,IAAIkM,EAAG,CAAC,CAAE,EAAE,KAAK,cAAc,KAAK,GAAGlM,CAAC,EAAE,KAAK,KAAK,iBAAiBkM,CAAC,CAAC,CAAC,UAAUA,EAAE,CAAC,IAAIlM,EAAE,EAAE,GAAG,CAAC,KAAK,WAAW,MAAM,MAAM,+BAA+B,EAAE,MAAMmM,EAAE,KAAK,WAAW,cAAc,GAAU,GAAUnM,EAAE,KAAK,cAAf,MAAqCA,IAAT,OAAW,OAAOA,EAAE,oBAA1D,MAAsF,IAAT,OAAW,OAAO,EAAE,iBAAiB,EAAE,IAAIU,GAAEwL,EAAEC,EAAE,CAAC,EAAE,OAAOA,EAAE,KAAK,WAAW,CAAC,EAAE,KAAK,cAAc,KAAK,KAAK,WAAW,KAAK,QAASD,GAAG,CAAC,EAAE,kBAAkBA,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC,oBAAoBA,EAAE,CAAC,IAAIlM,EAAEmM,EAAE,MAAMG,GAAUH,GAAUnM,EAAE,KAAK,cAAf,MAAqCA,IAAT,OAAW,OAAOA,EAAE,WAAY,KAAtE,MAAkFmM,IAAT,OAAW,OAAOA,EAAE,cAAc,KAAK,EAAE,GAAG,CAACG,EAAE,MAAM,IAAI,GAAG,IAAIF,EAAE,KAAKK,EAAE,EAAE,OAAO5L,GAAEyL,EAAG,CAACJ,EAAElM,EAAEa,IAAI,CAACuL,GAAGA,EAAE,UAAUF,EAAErL,EAAE4L,EAAE,MAAM,OAAO,CAAC,EAAIzM,GAAG,CAAC,IAAIa,EAAEsL,EAAE,GAAGM,EAAEzM,EAAE,CAAC,KAAK,WAAW,OAAO,MAAMsM,EAAE,KAAK,WAAW,YAAW,EAAGhF,GAAU6E,GAAUtL,EAAE,KAAK,cAAf,MAAqCA,IAAT,OAAW,OAAOA,EAAE,eAAc,KAAxE,MAAsFsL,IAAT,OAAW,OAAOA,EAAE,iBAAiBO,EAAE,KAAK,WAAW,WAAU,EAAG,YAAYL,EAAErM,EAAE0M,EAAEJ,EAAEC,GAAGvM,EAAE,GAAG0M,EAAEJ,EAAEF,EAAE,IAAI1L,GAAE,OAAO,OAAO,OAAO,OAAO,CAAE,EAACwL,CAAC,EAAE,CAAC,MAAMG,EAAE,IAAIE,CAAC,CAAC,EAAED,EAAEhF,CAAC,EAAE,KAAK,iBAAiB,YAAY8E,EAAE,OAAO,CAAC,EAAI,IAAI,CAACA,IAAI,KAAK,WAAWA,CAAC,EAAEA,EAAE,KAAK,EAAG,CAAC,cAAc,CAAC,KAAK,QAAQ,QAASF,GAAGA,EAAE,OAAQ,CAAA,CAAE,CAAC,SAAS,CAAC,KAAK,aAAY,EAAG,MAAM,QAAO,CAAE,CAAC,m0BCKrlT,OAAApG,MAAiB,EAAC,EAEbA,KAAgB,GAAG,EAEnBA,MAAiB,GAAG,uVAPlB,cAAA6G,CAAqB,EAAAC,kbCDvB,SAAAC,EAAA,SAAuB,8NAiCzB/G,EAAa,CAAA,UARrBvH,GAgBCC,EAAAsO,EAAApO,CAAA,yFAROoH,EAAa,CAAA,uEA9BT,GAAA,CAAA,cAAA6G,EAAgB,CAAC,EAAAC,EACjB,CAAA,mBAAAG,EAAqB,EAAK,EAAAH,GAC1B,SAAAf,CAAgC,EAAAe,EAEvCI,EAEJH,GAAO,IAAA,CACNI,YAGKA,EAAY,IAAA,CACb,IAAAC,EAASF,EACRE,IAELA,EAAO,MAAM,WAAU,iDACtBP,EAAgB,GACjB,yBAAyBA,EAAgB,GAAG,iDAOlCK,EAAaG,WAQJ,MAAAC,EAAA,IAAAC,EAAA,EAAAN,EAAqB,EAAK,IACnC/M,GAAC,CACPA,EAAE,kBAAkB,mBACvBqN,EAAA,EAAAV,EAAgB,WAAW3M,EAAE,OAAO,KAAK,CAAA,EACzC6L,GAAU,UAAUc,CAAa,2LAhBjBM,EAAY,8vDC+NvBnH,EAAS,CAAA,GAAIA,EAAI,CAAA,IAAK,IAAEwH,GAAAxH,CAAA,uCAcxB,OAAAA,OAAS,GAAE,oIAdXA,EAAS,CAAA,GAAIA,EAAI,CAAA,IAAK,ggBAC1BvH,GAUQC,EAAA+O,EAAA7O,CAAA,0WAYRH,GAA6DC,EAAAgP,EAAA9O,CAAA,YAC7DH,GACAC,EAAAiP,EAAA/O,CAAA,qBAFsCoH,EAAS,EAAA,CAAA,cACTA,EAAkB,EAAA,CAAA,qPATxDvH,GAMQC,EAAA+O,EAAA7O,CAAA,sCAHGoH,EAAkB,EAAA,CAAA,6OAtF1BA,EAAkB,CAAA,GAAA4H,GAAA5H,CAAA,6DA6CjBA,EAAO,CAAA,EAAA,yCAsBR,IAAA6H,EAAA7H,OAAYA,EAAW,CAAA,GAAA8H,GAAA9H,CAAA,yHA9CpBA,EAAa,EAAA,CAAA,OAAC,GAAC,mOA9BT+H,GAAAL,EAAA,QAAA1H,EAAA,CAAA,EACV,sBACA,oBAAoB,4FAetBA,EAAc,EAAA,GACZA,EAAc,EAAA,EAAC,QAAQA,EAAiB,EAAA,CAAA,EAAA,GAAKA,EAAe,EAAA,EAAA,MAAA,CAAA,GAAA,gBAJjDA,EAAkB,CAAA,CAAA,wFAuBCgI,EAAAC,EAAA,aAAAC,EAAA,qBAAAjC,GAChCjG,EACA,CAAA,EAAAA,EAAiB,CAAA,EAAA,WAAA,CAAA,UAAA,yEAaNA,EAAO,CAAA,EAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,CAAA,yCAUhCgI,EAAAG,EAAA,aAAAC,EAAA,mBAAAnC,GAC5BjG,EACA,CAAA,EAAAA,EAAiB,CAAA,EAAA,WAAA,EAAA,UAAA,qLApErBvH,GA8GKC,EAAA2P,EAAAzP,CAAA,EA7GJC,EAmCKwP,EAAAC,CAAA,EAlCJzP,EASQyP,EAAAZ,CAAA,4CAMR7O,EAkBQyP,EAAAX,CAAA,EADP9O,EAA4B8O,EAAAY,CAAA,uBAI9B1P,EAuCKwP,EAAAG,CAAA,EAtCJ3P,EAaQ2P,EAAAP,CAAA,sBACRpP,EAUQ2P,EAAAC,CAAA,wBACR5P,EAYQ2P,EAAAL,CAAA,sBAGTtP,EA8BKwP,EAAAK,CAAA,iNAzGUX,GAAAL,EAAA,QAAA1H,EAAA,CAAA,EACV,sBACA,oBAAoB,EAOnBA,EAAkB,CAAA,yHAqBfA,EAAa,EAAA,CAAA,qDAbnBA,EAAc,EAAA,GACZA,EAAc,EAAA,EAAC,QAAQA,EAAiB,EAAA,CAAA,EAAA,GAAKA,EAAe,EAAA,EAAA,MAAA,CAAA,uDAJjDA,EAAkB,CAAA,CAAA,GAuBC,CAAA2I,GAAAC,EAAA,CAAA,EAAA,KAAAV,KAAAA,EAAA,qBAAAjC,GAChCjG,EACA,CAAA,EAAAA,EAAiB,CAAA,EAAA,WAAA,CAAA,2KAaNA,EAAO,CAAA,EAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,0BAUhC,CAAA2I,GAAAC,EAAA,CAAA,EAAA,KAAAR,KAAAA,EAAA,mBAAAnC,GAC5BjG,EACA,CAAA,EAAAA,EAAiB,CAAA,EAAA,WAAA,EAAA,kCAYdA,OAAYA,EAAW,CAAA,sXAzOlB,SAAA+F,CAAgC,EAAAe,GAChC,eAAAZ,CAAsB,EAAAY,GACtB,KAAA+B,CAAmB,EAAA/B,GACnB,QAAAgC,CAAgB,EAAAhC,EAChB,CAAA,UAAAiC,EAAY,EAAK,EAAAjC,EACjB,CAAA,YAAAkC,EAAc,EAAK,EAAAlC,GACnB,kBAAAmC,CAAuD,EAAAnC,EACvD,CAAA,KAAAoC,EAAO,EAAE,EAAApC,GACT,UAAAlI,CAAyB,EAAAkI,GACzB,mBAAAqC,CAA8B,EAAArC,GAC9B,iBAAAsC,EAAgB,EAAA,EAAAtC,GAChB,qBAAAuC,EAAoB,EAAA,EAAAvC,EACpB,CAAA,mBAAAG,EAAqB,EAAK,EAAAH,EAC1B,CAAA,SAAAwC,EAAW,EAAI,EAAAxC,EAEf,CAAA,aAAAyC,EAAe,CAAC,EAAAzC,EAEvB0C,GAAkB,GAAK,EAAG,IAAK,CAAC,EAChCC,EAAgBD,EAAe,CAAC,EAEhCE,EAAmC,KACnCC,EAA8B,KAE9BC,EACAC,EACAC,EAAe,GAEfjD,EAAgB,QAqBdkD,EAAa,IAAA,CACbL,SACLC,EAAeD,GAAY,UAAS,CACnC,MAAOxD,EAAiB,EACxB,IAAKA,EAAiB,EACnB,GAAAmD,KAGJ9B,EAAA,GAAAgC,EAAeI,EAAa,IAAMA,EAAa,KAAK,IA2B/CK,EAAS,IAAA,CACV,GAAAjE,GAAY2D,GACXC,EAAY,OACTlI,EAAQkI,EAAa,MACrBjI,GAAMiI,EAAa,IACzBV,EAAkBxH,EAAOC,EAAG,EAC5B6F,EAAA,EAAA2B,EAAO,EAAE,EACT3B,EAAA,GAAAoC,EAAe,IAAI,IAKhBM,EAAY,IAAA,CACjBP,GAAY,WAAU,EAAG,QAASQ,GAAM,CACvCA,EAAO,OAAM,IAEdR,GAAY,aAAY,GAGnBS,GAAkB,IAAA,CACvBF,IACIf,IAAS,OACZ3B,EAAA,EAAA2B,EAAO,EAAE,GAET3B,EAAA,EAAA2B,EAAO,MAAM,EACba,MAIIK,GAAmB,CAAIC,EAAgBC,KAAW,KACnDC,GACAC,GAECb,IACDU,IAAW,OACVC,KAAQ,aACXC,GAAWZ,EAAa,MAAQ,IAChCa,GAASb,EAAa,MAEtBY,GAAWZ,EAAa,MAAQ,IAChCa,GAASb,EAAa,KAGnBW,KAAQ,aACXC,GAAWZ,EAAa,MACxBa,GAASb,EAAa,IAAM,MAE5BY,GAAWZ,EAAa,MACxBa,GAASb,EAAa,IAAM,KAI9BA,EAAa,WACZ,CAAA,MAAOY,GACP,IAAKC,EAAA,CAAA,EAGNjD,EAAA,GAAAgC,EAAeI,EAAa,IAAMA,EAAa,KAAK,IAqBlCc,EAAA,IAAAlD,EAAA,EAAAN,GAAsBA,CAAkB,oEAkBxDM,EAAA,GAAAkC,EACCD,GACEA,EAAe,QAAQC,CAAa,EAAI,GAAKD,EAAe,MAAA,CAAA,EAG/DzD,GAAU,gBAAgB0D,CAAa,SAevC1D,GAAU,KACTE,GAAuBC,EAAgBkD,EAAiB,WAAW,EACjE,EAAA,EAOYsB,EAAA,IAAA3E,GAAU,kBAgBzBA,GAAU,KACTE,GAAuBC,EAAgBkD,EAAiB,WAAW,CAAA,UAclED,IACAc,IACA1C,EAAA,EAAA2B,EAAO,EAAE,krBApNX3B,EAAA,GAAAmC,EACF9K,GAAamH,EACVA,EAAS,eAAe4E,GAAc,QAAM,EAC5C,IAAI,yBAELjB,GAAY,GAAG,aAAeQ,GAAM,CACtCA,EAAO,KAAI,2BAGTR,GAAY,GAAG,iBAAmBQ,GAAM,CAC1C3C,EAAA,GAAAgC,EAAeW,EAAO,IAAMA,EAAO,KAAK,2BAGtCR,GAAY,GAAG,iBAAgB,CAAGQ,EAAQhQ,KAAC,CAC7CA,GAAE,gBAAe,EACjBqN,EAAA,GAAAoC,EAAeO,CAAM,EACrBA,EAAO,KAAI,4BAcLP,EAAY,CACZ,MAAAiB,EAAahM,EAAU,SAAS,CAAC,EAAG,WAE1C2I,EAAA,GAAAsC,EAAoBe,EAAW,cAAc,uBAAuB,CAAA,EACpErD,EAAA,GAAAqC,EAAmBgB,EAAW,cAAc,sBAAsB,CAAA,EAE9DhB,GAAoBC,IACvBD,EAAiB,aAAa,OAAQ,QAAQ,EAC9CC,EAAkB,aAAa,OAAQ,QAAQ,EAC/CD,GAAkB,aAAa,aAAc,2BAA2B,EACxEC,GAAmB,aAAa,aAAc,yBAAyB,EACvED,GAAkB,aAAa,WAAY,GAAG,EAC9CC,GAAmB,aAAa,WAAY,GAAG,EAE/CD,EAAiB,iBAAiB,QAAO,IAAA,CACpCF,GAAUnC,EAAA,GAAEuC,EAAe,MAAM,IAGtCD,EAAkB,iBAAiB,QAAO,IAAA,CACrCH,GAAUnC,EAAA,GAAEuC,EAAe,OAAO,6BAiEtCJ,GACF,OAAO,iBAAiB,UAAYxP,GAAC,CAChCA,EAAE,MAAQ,YACbkQ,GAAoBN,EAAc,WAAW,EACnC5P,EAAE,MAAQ,cACpBkQ,GAAoBN,EAAc,YAAY,qpEC/JxC,CAAA,QAAA/C,EAAA,SAAuB,2BAUvB,CAAA,sBAAA8D,WAAqC,gFAyKtCC,EAAA9K,EAAS,CAAA,IAAA,QAAUA,MAAe,GAACwH,GAAAxH,CAAA,6MAmB9BA,EAAW,CAAA,skBA1BPA,EAAS,EAAA,EAAG,KAAO,MAAM,kQAN5BA,EAAK,CAAA,EAAG,YAAcA,EAAK,CAAA,EAAG,kBAAkB,UAF9DvH,GAyCKC,EAAAqS,EAAAnS,CAAA,EArCJC,EAMKkS,EAAAvC,CAAA,EALJ3P,EAIC2P,EAAAF,CAAA,kBAGFzP,EAQKkS,EAAA1C,CAAA,EAPJxP,EAA8CwP,EAAA2C,CAAA,kBAC9CnS,EAKKwP,EAAAK,CAAA,wBADJ7P,EAAsD6P,EAAAuC,CAAA,qEAVxCjL,EAAS,EAAA,EAAG,KAAO,MAAM,EAOlCA,EAAS,CAAA,IAAA,QAAUA,MAAe,8QAmB7BA,EAAW,CAAA,qVAhCVA,EAAK,CAAA,EAAG,YAAcA,EAAK,CAAA,EAAG,qRATtCkL,GAAAC,EAAA,IAAAC,EAAApL,KAAM,GAAG,GAAAgI,EAAAmD,EAAA,MAAAC,CAAA,gBAEJD,EAAA,SAAAE,EAAArL,KAAkB,6BAJ7BvH,GAOCC,EAAAyS,EAAAvS,CAAA,yCALKgQ,EAAA,CAAA,EAAA,GAAA,CAAAsC,GAAAC,EAAA,IAAAC,EAAApL,KAAM,GAAG,gBAEJ4I,EAAA,CAAA,EAAA,KAAAyC,KAAAA,EAAArL,KAAkB,mZAqBCsL,EAAAC,GAAYvL,EAAY,EAAA,CAAA,EAAA,oGAAlDvH,GAA0DC,EAAAsE,EAAApE,CAAA,iBAAhCgQ,EAAA,CAAA,EAAA,QAAA0C,KAAAA,EAAAC,GAAYvL,EAAY,EAAA,CAAA,EAAA,KAAAwL,GAAApF,EAAAkF,CAAA,kQA9BlD,OAAAtL,OAAU,KAAI,EAITA,KAAM,UAAS,wUA9Ib,CAAA,MAAAnG,EAAyB,IAAI,EAAAiN,GAE7B,MAAA2E,CAAa,EAAA3E,GACb,KAAA+B,CAAmB,EAAA/B,GACnB,cAAA4E,EAAa,IAGG,QAAQ,QAAO,CAAA,EAAA5E,EAC/B,CAAA,YAAAkC,EAAc,EAAK,EAAAlC,EACnB,CAAA,SAAAwC,EAAW,EAAI,EAAAxC,GACf,qBAAAuC,EAAoB,EAAA,EAAAvC,GACpB,kBAAA6E,CAAsC,EAAA7E,GACtC,iBAAAsC,CAAiC,EAAAtC,EACjC,CAAA,KAAAoC,EAAO,EAAE,EAAApC,GACT,KAAA8E,CAAa,EAAA9E,GACb,mBAAAqC,EAAkB,IAAA,MAEzBvK,EACAmH,EACA+C,EAAU,GAEV+C,EACAC,EACA5F,EAEAqD,EAAe,EAEftC,EAAqB,GAEnB,MAAA8E,EAAWlB,KASXmB,EAAe,IAAA,CACpBzE,EAAA,GAAAxB,EAAW/B,GAAW,OAAM,CAChB,UAAApF,EACR,GAAA+M,CAAA,CAAA,CAAA,EAEJM,GAAiBpS,GAAO,GAAG,EAAE,KAAMqS,GAAY,CAC1C,GAAAA,GAAgBnG,SACZA,EAAS,KAAKmG,CAAY,KAoD9BjD,EAAiB,MACtBxH,EACAC,IAAW,CAEX6F,EAAA,EAAA2B,EAAO,EAAE,QACHiD,EAAcpG,GAAU,iBAC1BoG,GACG,MAAA5G,GACL4G,EACA1K,EACAC,EACAiK,EAAkB,UAAU,EAC3B,WAAYS,IAAuB,OAC9BV,EAAa,CAAEU,EAAW,EAAG,QAAQ,EAC3CrG,GAAU,QAAO,OACjBnH,EAAU,UAAY,GAAEA,CAAA,IAE1BmN,EAAS,MAAM,GAGD,eAAAM,EAAWzH,EAAY,CAC/B,MAAAqH,GAAiBrH,CAAI,EAAE,KAAMsH,GAAY,OACzCA,GAAgBrS,GAAO,kBACrBkM,GAAU,KAAKmG,CAAY,IAMpCnF,GAAO,IAAA,CACN,OAAO,iBAAiB,UAAY7M,GAAC,CAC/B,CAAA6L,GAAYkB,IACb/M,EAAE,MAAQ,cAAgBgP,IAAS,OACtCpD,GAAWC,EAAU,EAAG,EACd7L,EAAE,MAAQ,aAAegP,IAAS,QAC5CpD,GAAWC,EAAQ,GAAM,kFA2BdnH,EAASyI,+DAMJwE,EAAOxE,8DAKNyE,EAAWzE,6mBAzK5BE,EAAA,GAAAjM,EAAMzB,GAAO,GAAG,sBAiDZ+E,IAAc,SAChBmH,IAAa,QAAWA,EAAS,QAAO,OAC5CnH,EAAU,UAAY,GAAEA,CAAA,EACxBoN,IACAzE,EAAA,GAAAuB,EAAU,EAAK,wBAGb/C,GAAU,GAAG,SAAW5K,GAAa,CACvCoM,EAAA,GAAArB,EAAiB/K,CAAQ,EACzB2Q,QAAgBA,EAAY,YAAcP,GAAYpQ,CAAQ,EAAA2Q,CAAA,yBAG5D/F,GAAU,GACZ,aACC7B,GACA2H,QAAYA,EAAQ,YAAcN,GAAYrH,CAAW,EAAA2H,CAAA,CAAA,sBAGxD9F,GAAU,GAAG,QAAO,IAAA,CACjB4F,EAAkB,SAGtB5F,GAAU,KAAI,EAFdA,GAAU,KAAI,wBAMbA,GAAU,GAAG,SAAQ,IAAA,CACnB6F,EACH7F,GAAU,KAAI,GAEdwB,EAAA,GAAAuB,EAAU,EAAK,EACfiD,EAAS,MAAM,yBAGdhG,GAAU,GAAG,QAAO,IAAA,CACtBwB,EAAA,GAAAuB,EAAU,EAAK,EACfiD,EAAS,OAAO,wBAEdhG,GAAU,GAAG,OAAM,IAAA,CACrBwB,EAAA,GAAAuB,EAAU,EAAI,EACdiD,EAAS,MAAM,wBAGbhG,GAAU,GAAG,OAAM,IAAA,CACrBgG,EAAS,MAAM,2BA8BbzQ,GAAO+Q,EAAW/Q,CAAG,qpDClIf,CAAA,sBAAAuP,WAAqC,kWAoCxC7K,EAAoB,CAAA,GAAAwH,GAAAxH,CAAA,IAKpBA,EAAiB,CAAA,GAAAsM,GAAAtM,CAAA,yUANvBvH,GAmBKC,EAAAgG,EAAA9F,CAAA,yEAlBCoH,EAAoB,CAAA,gGAKpBA,EAAiB,CAAA,soBAJD,KAAAA,KAAM,IAAe,SAAAA,EAAM,CAAA,EAAA,WAAaA,KAAM,gHAA9C4I,EAAA,IAAA2D,EAAA,KAAAvM,KAAM,KAAe4I,EAAA,IAAA2D,EAAA,SAAAvM,EAAM,CAAA,EAAA,WAAaA,KAAM,2LAC/CwM,GAAiB,MAAAxM,KAAK,iBAAiB,sEAAtB4I,EAAA,KAAA6D,EAAA,MAAAzM,KAAK,iBAAiB,weATtD0M,SACC,SACA1M,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,0CAG9B,OAAAA,OAAU,KAAI,gMAHXA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,mSA1BvB,GAAA,CAAA,MAAAnG,EAAyB,IAAI,EAAAiN,GAC7B,MAAA2E,CAAa,EAAA3E,EACb,CAAA,WAAA6F,EAAa,EAAI,EAAA7F,EACjB,CAAA,qBAAA8F,EAAuB,EAAI,EAAA9F,EAC3B,CAAA,kBAAA+F,EAAoB,EAAK,EAAA/F,GACzB,KAAA+B,CAAmB,EAAA/B,GACnB,kBAAA6E,CAAsC,EAAA7E,GACtC,iBAAAsC,CAAiC,EAAAtC,EACjC,CAAA,SAAAwC,EAAW,EAAI,EAAAxC,GACf,KAAA8E,CAAa,EAAA9E,EAElB,MAAAiF,EAAWlB,aA8BIhR,GACZA,gCACWiT,GAAoBjT,EAAM,GAAU,CAClB,aAFf,8nBAvBpBA,GAASkS,EAAS,SAAUlS,CAAK", "x_google_ignoreList": [5, 6, 7, 8, 9, 10, 11, 12, 13, 16]}