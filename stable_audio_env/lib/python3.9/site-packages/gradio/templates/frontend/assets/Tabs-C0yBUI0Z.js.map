{"version": 3, "file": "Tabs-C0yBUI0Z.js", "sources": ["../../../../js/tabs/shared/Tabs.svelte"], "sourcesContent": ["<script context=\"module\">\n\texport const TABS = {};\n</script>\n\n<script lang=\"ts\">\n\timport { setContext, createEventDispatcher } from \"svelte\";\n\timport { writable } from \"svelte/store\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\tinterface Tab {\n\t\tname: string;\n\t\tid: object;\n\t\telem_id: string | undefined;\n\t\tvisible: boolean;\n\t\tinteractive: boolean;\n\t}\n\n\texport let visible = true;\n\texport let elem_id = \"id\";\n\texport let elem_classes: string[] = [];\n\texport let selected: number | string | object;\n\n\tlet tabs: Tab[] = [];\n\n\tconst selected_tab = writable<false | object | number | string>(false);\n\tconst selected_tab_index = writable<number>(0);\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\tsetContext(TABS, {\n\t\tregister_tab: (tab: Tab) => {\n\t\t\tlet index: number;\n\t\t\tlet existingTab = tabs.find((t) => t.id === tab.id);\n\t\t\tif (existingTab) {\n\t\t\t\t// update existing tab with newer values\n\t\t\t\tindex = tabs.findIndex((t) => t.id === tab.id);\n\t\t\t\ttabs[index] = { ...tabs[index], ...tab };\n\t\t\t} else {\n\t\t\t\ttabs.push({\n\t\t\t\t\tname: tab.name,\n\t\t\t\t\tid: tab.id,\n\t\t\t\t\telem_id: tab.elem_id,\n\t\t\t\t\tvisible: tab.visible,\n\t\t\t\t\tinteractive: tab.interactive\n\t\t\t\t});\n\t\t\t\tindex = tabs.length - 1;\n\t\t\t}\n\t\t\tselected_tab.update((current) => {\n\t\t\t\tif (current === false && tab.visible && tab.interactive) {\n\t\t\t\t\treturn tab.id;\n\t\t\t\t}\n\n\t\t\t\tlet nextTab = tabs.find((t) => t.visible && t.interactive);\n\t\t\t\treturn nextTab ? nextTab.id : current;\n\t\t\t});\n\t\t\ttabs = tabs;\n\t\t\treturn index;\n\t\t},\n\t\tunregister_tab: (tab: Tab) => {\n\t\t\tconst i = tabs.findIndex((t) => t.id === tab.id);\n\t\t\ttabs.splice(i, 1);\n\t\t\tselected_tab.update((current) =>\n\t\t\t\tcurrent === tab.id ? tabs[i]?.id || tabs[tabs.length - 1]?.id : current\n\t\t\t);\n\t\t},\n\t\tselected_tab,\n\t\tselected_tab_index\n\t});\n\n\tfunction change_tab(id: object | string | number): void {\n\t\tconst tab_to_activate = tabs.find((t) => t.id === id);\n\t\tif (\n\t\t\ttab_to_activate &&\n\t\t\ttab_to_activate.interactive &&\n\t\t\ttab_to_activate.visible\n\t\t) {\n\t\t\tselected = id;\n\t\t\t$selected_tab = id;\n\t\t\t$selected_tab_index = tabs.findIndex((t) => t.id === id);\n\t\t\tdispatch(\"change\");\n\t\t} else {\n\t\t\tconsole.warn(\"Attempted to select a non-interactive or hidden tab.\");\n\t\t}\n\t}\n\n\t$: tabs, selected !== null && change_tab(selected);\n</script>\n\n<div class=\"tabs {elem_classes.join(' ')}\" class:hide={!visible} id={elem_id}>\n\t<div class=\"tab-nav scroll-hide\" role=\"tablist\">\n\t\t{#each tabs as t, i (t.id)}\n\t\t\t{#if t.visible}\n\t\t\t\t{#if t.id === $selected_tab}\n\t\t\t\t\t<button\n\t\t\t\t\t\trole=\"tab\"\n\t\t\t\t\t\tclass=\"selected\"\n\t\t\t\t\t\taria-selected={true}\n\t\t\t\t\t\taria-controls={t.elem_id}\n\t\t\t\t\t\tid={t.elem_id ? t.elem_id + \"-button\" : null}\n\t\t\t\t\t>\n\t\t\t\t\t\t{t.name}\n\t\t\t\t\t</button>\n\t\t\t\t{:else}\n\t\t\t\t\t<button\n\t\t\t\t\t\trole=\"tab\"\n\t\t\t\t\t\taria-selected={false}\n\t\t\t\t\t\taria-controls={t.elem_id}\n\t\t\t\t\t\tdisabled={!t.interactive}\n\t\t\t\t\t\taria-disabled={!t.interactive}\n\t\t\t\t\t\tid={t.elem_id ? t.elem_id + \"-button\" : null}\n\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\tchange_tab(t.id);\n\t\t\t\t\t\t\tdispatch(\"select\", { value: t.name, index: i });\n\t\t\t\t\t\t}}\n\t\t\t\t\t>\n\t\t\t\t\t\t{t.name}\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t{/if}\n\t\t{/each}\n\t</div>\n\t<slot />\n</div>\n\n<style>\n\t.tabs {\n\t\tposition: relative;\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.tab-nav {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-wrap: wrap;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\tbutton {\n\t\tmargin-bottom: -1px;\n\t\tborder: 1px solid transparent;\n\t\tborder-color: transparent;\n\t\tborder-bottom: none;\n\t\tborder-top-right-radius: var(--container-radius);\n\t\tborder-top-left-radius: var(--container-radius);\n\t\tpadding: var(--size-1) var(--size-4);\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--section-header-text-size);\n\t}\n\n\tbutton:disabled {\n\t\tcolor: var(--body-text-color-subdued);\n\t\topacity: 0.5;\n\t\tcursor: not-allowed;\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\t.selected {\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground: var(--background-fill-primary);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.bar {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\tbottom: -2px;\n\t\tleft: 0;\n\t\tz-index: 999;\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: 100%;\n\t\theight: 2px;\n\t\tcontent: \"\";\n\t}\n</style>\n"], "names": ["createEventDispatcher", "ctx", "create_if_block_1", "t0_value", "attr", "button", "button_aria_controls_value", "button_disabled_value", "button_aria_disabled_value", "button_id_value", "insert", "target", "anchor", "dirty", "set_data", "t0", "if_block", "create_if_block", "get_key", "i", "div1", "append", "div0", "TABS", "visible", "$$props", "elem_id", "elem_classes", "selected", "tabs", "selected_tab", "writable", "selected_tab_index", "dispatch", "setContext", "tab", "index", "t", "$$invalidate", "current", "nextTab", "change_tab", "id", "tab_to_activate", "set_store_value", "$selected_tab", "$selected_tab_index"], "mappings": "ucAKoB,sBAAAA,IAAuB,OAAgB,sIAyFlDC,EAAC,EAAA,EAAC,KAAOA,EAAa,CAAA,EAAAC,qMAuBxBC,EAAAF,MAAE,KAAI,sIAVQ,EAAK,EACLG,EAAAC,EAAA,gBAAAC,EAAAL,MAAE,OAAO,EACbI,EAAA,SAAAE,EAAA,CAAAN,MAAE,YACGG,EAAAC,EAAA,gBAAAG,EAAA,CAAAP,MAAE,WAAW,EACzBG,EAAAC,EAAA,KAAAI,EAAAR,MAAE,QAAUA,EAAC,EAAA,EAAC,QAAU,UAAY,IAAI,wCAN7CS,EAaQC,EAAAN,EAAAO,CAAA,uDADNC,EAAA,GAAAV,KAAAA,EAAAF,MAAE,KAAI,KAAAa,EAAAC,EAAAZ,CAAA,EATQU,EAAA,GAAAP,KAAAA,EAAAL,MAAE,iCACNY,EAAA,GAAAN,KAAAA,EAAA,CAAAN,MAAE,6BACGY,EAAA,GAAAL,KAAAA,EAAA,CAAAP,MAAE,qCACdY,EAAA,GAAAJ,KAAAA,EAAAR,MAAE,QAAUA,EAAC,EAAA,EAAC,QAAU,UAAY,iEATvCE,EAAAF,MAAE,KAAI,8HAJQ,EAAI,EACJG,EAAAC,EAAA,gBAAAC,EAAAL,MAAE,OAAO,EACpBG,EAAAC,EAAA,KAAAI,EAAAR,MAAE,QAAUA,EAAC,EAAA,EAAC,QAAU,UAAY,IAAI,UAL7CS,EAQQC,EAAAN,EAAAO,CAAA,wBADNC,EAAA,GAAAV,KAAAA,EAAAF,MAAE,KAAI,KAAAa,EAAAC,EAAAZ,CAAA,EAHQU,EAAA,GAAAP,KAAAA,EAAAL,MAAE,iCACbY,EAAA,GAAAJ,KAAAA,EAAAR,MAAE,QAAUA,EAAC,EAAA,EAAC,QAAU,UAAY,2DAPtCe,EAAAf,MAAE,SAAOgB,EAAAhB,CAAA,kHAATA,MAAE,0JADDA,EAAI,CAAA,CAAA,EAAU,MAAAiB,EAAAjB,GAAAA,MAAE,mBAArB,OAAIkB,GAAA,EAAA,yQAFUlB,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,WAA8BA,EAAO,CAAA,CAAA,cAApBA,EAAO,CAAA,CAAA,UAA/DS,EAkCKC,EAAAS,EAAAR,CAAA,EAjCJS,EA+BKD,EAAAE,CAAA,oGA9BGrB,EAAI,CAAA,CAAA,mIAFKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,wDAA8BA,EAAO,CAAA,CAAA,yBAApBA,EAAO,CAAA,CAAA,gHAzFjDsB,GAAI,CAAA,wDAgBN,CAAA,QAAAC,EAAU,EAAI,EAAAC,EACd,CAAA,QAAAC,EAAU,IAAI,EAAAD,GACd,aAAAE,EAAY,EAAA,EAAAF,GACZ,SAAAG,CAAkC,EAAAH,EAEzCI,EAAI,CAAA,QAEFC,EAAeC,EAA2C,EAAK,2BAC/DC,EAAqBD,EAAiB,CAAC,sBACvC,MAAAE,EAAWjC,KAKjBkC,EAAWX,GAAI,CACd,aAAeY,GAAQ,KAClBC,SACcP,EAAK,KAAMQ,GAAMA,EAAE,KAAOF,EAAI,EAAE,GAGjDC,EAAQP,EAAK,UAAWQ,GAAMA,EAAE,KAAOF,EAAI,EAAE,EAC7CG,EAAA,EAAAT,EAAKO,CAAK,EAAA,CAAA,GAASP,EAAKO,CAAK,KAAMD,CAAG,EAAAN,CAAA,IAEtCA,EAAK,KAAI,CACR,KAAMM,EAAI,KACV,GAAIA,EAAI,GACR,QAASA,EAAI,QACb,QAASA,EAAI,QACb,YAAaA,EAAI,cAElBC,EAAQP,EAAK,OAAS,GAEvBC,EAAa,OAAQS,GAAO,IACvBA,IAAY,IAASJ,EAAI,SAAWA,EAAI,YACpC,OAAAA,EAAI,GAGR,IAAAK,EAAUX,EAAK,KAAMQ,GAAMA,EAAE,SAAWA,EAAE,WAAW,EAClD,OAAAG,EAAUA,EAAQ,GAAKD,WAGxBH,GAER,eAAiBD,GAAQ,CAClB,MAAAhB,EAAIU,EAAK,UAAWQ,GAAMA,EAAE,KAAOF,EAAI,EAAE,EAC/CN,EAAK,OAAOV,EAAG,CAAC,EAChBW,EAAa,OAAQS,GACpBA,IAAYJ,EAAI,GAAKN,EAAKV,CAAC,GAAG,IAAMU,EAAKA,EAAK,OAAS,CAAC,GAAG,GAAKU,CAAO,GAGzE,aAAAT,EACA,mBAAAE,IAGQ,SAAAS,EAAWC,EAA4B,OACzCC,EAAkBd,EAAK,KAAMQ,GAAMA,EAAE,KAAOK,CAAE,EAEnDC,GACAA,EAAgB,aAChBA,EAAgB,SAEhBL,EAAA,EAAAV,EAAWc,CAAE,EACbE,EAAAd,EAAAe,EAAgBH,EAAEG,CAAA,MAClBC,EAAsBjB,EAAK,UAAWQ,GAAMA,EAAE,KAAOK,CAAE,EAAAI,CAAA,EACvDb,EAAS,QAAQ,GAEjB,QAAQ,KAAK,sDAAsD,kBA8B/DQ,EAAWJ,EAAE,EAAE,EACfJ,EAAS,SAAQ,CAAI,MAAOI,EAAE,KAAM,MAAOlB,CAAC,CAAA,qOA3BzCS,IAAa,MAAQa,EAAWb,CAAQ"}