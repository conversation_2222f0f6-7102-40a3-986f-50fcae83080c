{"version": 3, "file": "Textbox-BxQ_qaNA.js", "sources": ["../../../../js/textbox/shared/Textbox.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport {\n\t\tbeforeUpdate,\n\t\tafterUpdate,\n\t\tcreateEventDispatcher,\n\t\ttick\n\t} from \"svelte\";\n\timport { BlockTitle } from \"@gradio/atoms\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { fade } from \"svelte/transition\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let value = \"\";\n\texport let value_is_output = false;\n\texport let lines = 1;\n\texport let placeholder = \"Type here...\";\n\texport let label: string;\n\texport let info: string | undefined = undefined;\n\texport let disabled = false;\n\texport let show_label = true;\n\texport let container = true;\n\texport let max_lines: number;\n\texport let type: \"text\" | \"password\" | \"email\" = \"text\";\n\texport let show_copy_button = false;\n\texport let rtl = false;\n\texport let autofocus = false;\n\texport let text_align: \"left\" | \"right\" | undefined = undefined;\n\texport let autoscroll = true;\n\texport let max_length: number | undefined = undefined;\n\n\tlet el: HTMLTextAreaElement | HTMLInputElement;\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\tlet can_scroll: boolean;\n\tlet previous_scroll_top = 0;\n\tlet user_has_scrolled_up = false;\n\n\t$: value, el && lines !== max_lines && resize({ target: el });\n\n\t$: if (value === null) value = \"\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string;\n\t\tsubmit: undefined;\n\t\tblur: undefined;\n\t\tselect: SelectData;\n\t\tinput: undefined;\n\t\tfocus: undefined;\n\t}>();\n\n\tbeforeUpdate(() => {\n\t\tcan_scroll = el && el.offsetHeight + el.scrollTop > el.scrollHeight - 100;\n\t});\n\n\tconst scroll = (): void => {\n\t\tif (can_scroll && autoscroll && !user_has_scrolled_up) {\n\t\t\tel.scrollTo(0, el.scrollHeight);\n\t\t}\n\t};\n\n\tfunction handle_change(): void {\n\t\tdispatch(\"change\", value);\n\t\tif (!value_is_output) {\n\t\t\tdispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tif (autofocus) {\n\t\t\tel.focus();\n\t\t}\n\t\tif (can_scroll && autoscroll) {\n\t\t\tscroll();\n\t\t}\n\t\tvalue_is_output = false;\n\t});\n\t$: value, handle_change();\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tfunction handle_select(event: Event): void {\n\t\tconst target: HTMLTextAreaElement | HTMLInputElement = event.target as\n\t\t\t| HTMLTextAreaElement\n\t\t\t| HTMLInputElement;\n\t\tconst text = target.value;\n\t\tconst index: [number, number] = [\n\t\t\ttarget.selectionStart as number,\n\t\t\ttarget.selectionEnd as number\n\t\t];\n\t\tdispatch(\"select\", { value: text.substring(...index), index: index });\n\t}\n\n\tasync function handle_keypress(e: KeyboardEvent): Promise<void> {\n\t\tawait tick();\n\t\tif (e.key === \"Enter\" && e.shiftKey && lines > 1) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t} else if (\n\t\t\te.key === \"Enter\" &&\n\t\t\t!e.shiftKey &&\n\t\t\tlines === 1 &&\n\t\t\tmax_lines >= 1\n\t\t) {\n\t\t\te.preventDefault();\n\t\t\tdispatch(\"submit\");\n\t\t}\n\t}\n\n\tfunction handle_scroll(event: Event): void {\n\t\tconst target = event.target as HTMLElement;\n\t\tconst current_scroll_top = target.scrollTop;\n\t\tif (current_scroll_top < previous_scroll_top) {\n\t\t\tuser_has_scrolled_up = true;\n\t\t}\n\t\tprevious_scroll_top = current_scroll_top;\n\n\t\tconst max_scroll_top = target.scrollHeight - target.clientHeight;\n\t\tconst user_has_scrolled_to_bottom = current_scroll_top >= max_scroll_top;\n\t\tif (user_has_scrolled_to_bottom) {\n\t\t\tuser_has_scrolled_up = false;\n\t\t}\n\t}\n\n\tasync function resize(\n\t\tevent: Event | { target: HTMLTextAreaElement | HTMLInputElement }\n\t): Promise<void> {\n\t\tawait tick();\n\t\tif (lines === max_lines) return;\n\n\t\tlet max =\n\t\t\tmax_lines === undefined\n\t\t\t\t? false\n\t\t\t\t: max_lines === undefined // default\n\t\t\t\t\t? 21 * 11\n\t\t\t\t\t: 21 * (max_lines + 1);\n\t\tlet min = 21 * (lines + 1);\n\n\t\tconst target = event.target as HTMLTextAreaElement;\n\t\ttarget.style.height = \"1px\";\n\n\t\tlet scroll_height;\n\t\tif (max && target.scrollHeight > max) {\n\t\t\tscroll_height = max;\n\t\t} else if (target.scrollHeight < min) {\n\t\t\tscroll_height = min;\n\t\t} else {\n\t\t\tscroll_height = target.scrollHeight;\n\t\t}\n\n\t\ttarget.style.height = `${scroll_height}px`;\n\t}\n\n\tfunction text_area_resize(\n\t\t_el: HTMLTextAreaElement,\n\t\t_value: string\n\t): any | undefined {\n\t\tif (lines === max_lines) return;\n\t\t_el.style.overflowY = \"scroll\";\n\t\t_el.addEventListener(\"input\", resize);\n\n\t\tif (!_value.trim()) return;\n\t\tresize({ target: _el });\n\n\t\treturn {\n\t\t\tdestroy: () => _el.removeEventListener(\"input\", resize)\n\t\t};\n\t}\n</script>\n\n<!-- svelte-ignore a11y-autofocus -->\n<label class:container>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t{#if lines === 1 && max_lines === 1}\n\t\t{#if type === \"text\"}\n\t\t\t<input\n\t\t\t\tdata-testid=\"textbox\"\n\t\t\t\ttype=\"text\"\n\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\tbind:value\n\t\t\t\tbind:this={el}\n\t\t\t\t{placeholder}\n\t\t\t\t{disabled}\n\t\t\t\t{autofocus}\n\t\t\t\tmaxlength={max_length}\n\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\ton:blur\n\t\t\t\ton:select={handle_select}\n\t\t\t\ton:focus\n\t\t\t\tstyle={text_align ? \"text-align: \" + text_align : \"\"}\n\t\t\t/>\n\t\t{:else if type === \"password\"}\n\t\t\t<input\n\t\t\t\tdata-testid=\"password\"\n\t\t\t\ttype=\"password\"\n\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\tbind:value\n\t\t\t\tbind:this={el}\n\t\t\t\t{placeholder}\n\t\t\t\t{disabled}\n\t\t\t\t{autofocus}\n\t\t\t\tmaxlength={max_length}\n\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\ton:blur\n\t\t\t\ton:select={handle_select}\n\t\t\t\ton:focus\n\t\t\t\tautocomplete=\"\"\n\t\t\t/>\n\t\t{:else if type === \"email\"}\n\t\t\t<input\n\t\t\t\tdata-testid=\"textbox\"\n\t\t\t\ttype=\"email\"\n\t\t\t\tclass=\"scroll-hide\"\n\t\t\t\tbind:value\n\t\t\t\tbind:this={el}\n\t\t\t\t{placeholder}\n\t\t\t\t{disabled}\n\t\t\t\t{autofocus}\n\t\t\t\tmaxlength={max_length}\n\t\t\t\ton:keypress={handle_keypress}\n\t\t\t\ton:blur\n\t\t\t\ton:select={handle_select}\n\t\t\t\ton:focus\n\t\t\t\tautocomplete=\"email\"\n\t\t\t/>\n\t\t{/if}\n\t{:else}\n\t\t{#if show_label && show_copy_button}\n\t\t\t{#if copied}\n\t\t\t\t<button\n\t\t\t\t\tin:fade={{ duration: 300 }}\n\t\t\t\t\taria-label=\"Copied\"\n\t\t\t\t\taria-roledescription=\"Text copied\"><Check /></button\n\t\t\t\t>\n\t\t\t{:else}\n\t\t\t\t<button\n\t\t\t\t\ton:click={handle_copy}\n\t\t\t\t\taria-label=\"Copy\"\n\t\t\t\t\taria-roledescription=\"Copy text\"><Copy /></button\n\t\t\t\t>\n\t\t\t{/if}\n\t\t{/if}\n\t\t<textarea\n\t\t\tdata-testid=\"textbox\"\n\t\t\tuse:text_area_resize={value}\n\t\t\tclass=\"scroll-hide\"\n\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\tbind:value\n\t\t\tbind:this={el}\n\t\t\t{placeholder}\n\t\t\trows={lines}\n\t\t\t{disabled}\n\t\t\t{autofocus}\n\t\t\tmaxlength={max_length}\n\t\t\ton:keypress={handle_keypress}\n\t\t\ton:blur\n\t\t\ton:select={handle_select}\n\t\t\ton:focus\n\t\t\ton:scroll={handle_scroll}\n\t\t\tstyle={text_align ? \"text-align: \" + text_align : \"\"}\n\t\t/>\n\t{/if}\n</label>\n\n<style>\n\tlabel {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t}\n\n\tinput,\n\ttextarea {\n\t\tdisplay: block;\n\t\tposition: relative;\n\t\toutline: none !important;\n\t\tbox-shadow: var(--input-shadow);\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--input-padding);\n\t\twidth: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--input-text-weight);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t\tborder: none;\n\t}\n\tlabel:not(.container),\n\tlabel:not(.container) > input,\n\tlabel:not(.container) > textarea {\n\t\theight: 100%;\n\t}\n\t.container > input,\n\t.container > textarea {\n\t\tborder: var(--input-border-width) solid var(--input-border-color);\n\t\tborder-radius: var(--input-radius);\n\t}\n\tinput:disabled,\n\ttextarea:disabled {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\t-webkit-opacity: 1;\n\t\topacity: 1;\n\t}\n\n\tinput:focus,\n\ttextarea:focus {\n\t\tbox-shadow: var(--input-shadow-focus);\n\t\tborder-color: var(--input-border-color-focus);\n\t}\n\n\tinput::placeholder,\n\ttextarea::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\tbutton {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\talign-items: center;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--color-border-primary);\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t\tbackground: var(--block-label-background-fill);\n\t\tpadding: 5px;\n\t\twidth: 22px;\n\t\theight: 22px;\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-color);\n\t\tfont: var(--font-sans);\n\t\tfont-size: var(--button-small-text-size);\n\t}\n</style>\n"], "names": ["beforeUpdate", "createEventDispatcher", "tick", "ctx", "if_block", "create_if_block_4", "insert", "target", "textarea", "anchor", "create_if_block_1", "create_if_block_2", "create_if_block_3", "button", "button_intro", "create_in_transition", "fade", "input", "label_1", "value", "$$props", "value_is_output", "lines", "placeholder", "label", "info", "disabled", "show_label", "container", "max_lines", "type", "show_copy_button", "rtl", "autofocus", "text_align", "autoscroll", "max_length", "el", "copied", "timer", "can_scroll", "previous_scroll_top", "user_has_scrolled_up", "dispatch", "scroll", "handle_change", "afterUpdate", "$$invalidate", "handle_copy", "copy_feedback", "handle_select", "event", "text", "index", "handle_keypress", "e", "handle_scroll", "current_scroll_top", "max_scroll_top", "resize", "max", "min", "scroll_height", "text_area_resize", "_el", "_value", "$$value"], "mappings": "otBAEE,CAAA,aAAAA,kBACW,sBAAAC,GAEX,KAAAC,UACc,gEAiLkBC,EAAK,CAAA,CAAA,wCAALA,EAAK,CAAA,CAAA,qDAyDhCC,EAAAD,MAAcA,EAAgB,EAAA,GAAAE,EAAAF,CAAA,gIAmB7BA,EAAG,EAAA,EAAG,MAAQ,KAAK,qCAIlBA,EAAK,CAAA,CAAA,oDAGAA,EAAU,EAAA,CAAA,gBAMdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,EAAE,+BAjBrDG,EAkBCC,EAAAC,EAAAC,CAAA,yEAhBsBN,EAAK,CAAA,CAAA,CAAA,oCAUdA,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,kCAEbA,EAAa,EAAA,CAAA,iBA/BpBA,MAAcA,EAAgB,EAAA,qIAmB7BA,EAAG,EAAA,EAAG,MAAQ,qFAIbA,EAAK,CAAA,CAAA,0GAGAA,EAAU,EAAA,CAAA,yBAMdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,iEAf5BA,EAAK,CAAA,CAAA,qJAvEvB,GAAAA,OAAS,OAAM,OAAAO,GAkBV,GAAAP,OAAS,WAAU,OAAAQ,GAiBnB,GAAAR,OAAS,QAAO,OAAAS,8QAoBpBT,EAAM,EAAA,EAAA,meAOVG,EAIAC,EAAAM,EAAAJ,CAAA,qCAHWN,EAAW,EAAA,CAAA,ySAPtBG,EAIAC,EAAAM,EAAAJ,CAAA,mEAHYK,EAAAC,GAAAF,EAAAG,GAAA,CAAA,SAAU,GAAG,CAAA,8RAZdb,EAAU,EAAA,CAAA,sCATtBG,EAeCC,EAAAU,EAAAR,CAAA,+EALaN,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,0JAHbA,EAAU,EAAA,CAAA,oSAjBVA,EAAU,EAAA,CAAA,iCATtBG,EAeCC,EAAAU,EAAAR,CAAA,+EALaN,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,0JAHbA,EAAU,EAAA,CAAA,qOAvBhBA,EAAG,EAAA,EAAG,MAAQ,KAAK,4EAMbA,EAAU,EAAA,CAAA,gBAKdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,EAAE,UAfrDG,EAgBCC,EAAAU,EAAAR,CAAA,+EALaN,EAAe,EAAA,CAAA,iCAEjBA,EAAa,EAAA,CAAA,sDATnBA,EAAG,EAAA,EAAG,MAAQ,2IAMRA,EAAU,EAAA,CAAA,oBAKdA,EAAU,EAAA,EAAG,eAAiBA,EAAU,EAAA,EAAG,qPAjBhD,OAAAA,EAAU,CAAA,IAAA,GAAKA,OAAc,EAAC,6IAHpCG,EA6FOC,EAAAW,EAAAT,CAAA,gdAvQK,GAAA,CAAA,MAAAU,EAAQ,EAAE,EAAAC,EACV,CAAA,gBAAAC,EAAkB,EAAK,EAAAD,EACvB,CAAA,MAAAE,EAAQ,CAAC,EAAAF,EACT,CAAA,YAAAG,EAAc,cAAc,EAAAH,GAC5B,MAAAI,CAAa,EAAAJ,EACb,CAAA,KAAAK,EAA2B,MAAS,EAAAL,EACpC,CAAA,SAAAM,EAAW,EAAK,EAAAN,EAChB,CAAA,WAAAO,EAAa,EAAI,EAAAP,EACjB,CAAA,UAAAQ,EAAY,EAAI,EAAAR,GAChB,UAAAS,CAAiB,EAAAT,EACjB,CAAA,KAAAU,EAAsC,MAAM,EAAAV,EAC5C,CAAA,iBAAAW,EAAmB,EAAK,EAAAX,EACxB,CAAA,IAAAY,EAAM,EAAK,EAAAZ,EACX,CAAA,UAAAa,EAAY,EAAK,EAAAb,EACjB,CAAA,WAAAc,EAA2C,MAAS,EAAAd,EACpD,CAAA,WAAAe,EAAa,EAAI,EAAAf,EACjB,CAAA,WAAAgB,EAAiC,MAAS,EAAAhB,EAEjDiB,EACAC,EAAS,GACTC,EACAC,EACAC,EAAsB,EACtBC,EAAuB,GAMrB,MAAAC,EAAW1C,KASjBD,GAAY,IAAA,CACXwC,EAAaH,GAAMA,EAAG,aAAeA,EAAG,UAAYA,EAAG,aAAe,YAGjEO,GAAM,IAAA,CACPJ,GAAcL,GAAU,CAAKO,GAChCL,EAAG,SAAS,EAAGA,EAAG,YAAY,YAIvBQ,IAAa,CACrBF,EAAS,SAAUxB,CAAK,EACnBE,GACJsB,EAAS,OAAO,EAGlBG,GAAW,IAAA,CACNb,GACHI,EAAG,MAAK,EAELG,GAAcL,GACjBS,KAEDG,EAAA,GAAA1B,EAAkB,EAAK,mBAIT2B,IAAW,CACrB,cAAe,YACZ,MAAA,UAAU,UAAU,UAAU7B,CAAK,EACzC8B,eAIOA,IAAa,CACrBF,EAAA,GAAAT,EAAS,EAAI,EACTC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,gBACPQ,EAAA,GAAAT,EAAS,EAAK,GACZ,KAGK,SAAAY,GAAcC,EAAY,OAC5B5C,EAAiD4C,EAAM,OAGvDC,EAAO7C,EAAO,MACd8C,GACL9C,EAAO,eACPA,EAAO,YAAA,EAERoC,EAAS,SAAQ,CAAI,MAAOS,EAAK,UAAS,GAAIC,CAAK,EAAU,MAAAA,CAAK,CAAA,EAGpD,eAAAC,GAAgBC,EAAgB,OACxCrD,EAAI,GACNqD,EAAE,MAAQ,SAAWA,EAAE,UAAYjC,EAAQ,GAI9CiC,EAAE,MAAQ,UACTA,EAAE,UACHjC,IAAU,GACVO,GAAa,KAEb0B,EAAE,eAAc,EAChBZ,EAAS,QAAQ,GAIV,SAAAa,GAAcL,EAAY,OAC5B5C,EAAS4C,EAAM,OACfM,EAAqBlD,EAAO,UAC9BkD,EAAqBhB,IACxBC,EAAuB,IAExBD,EAAsBgB,EAEhB,MAAAC,EAAiBnD,EAAO,aAAeA,EAAO,aAChBkD,GAAsBC,IAEzDhB,EAAuB,IAIV,eAAAiB,EACdR,EAAiE,CAG7D,SADEjD,EAAI,EACNoB,IAAUO,EAAS,WAEnB+B,EACH/B,IAAc,OACX,GACAA,IAAc,OACb,GAAK,GACL,IAAMA,EAAY,GACnBgC,EAAM,IAAMvC,EAAQ,SAElBf,EAAS4C,EAAM,OACrB5C,EAAO,MAAM,OAAS,UAElBuD,EACAF,GAAOrD,EAAO,aAAeqD,EAChCE,EAAgBF,EACNrD,EAAO,aAAesD,EAChCC,EAAgBD,EAEhBC,EAAgBvD,EAAO,aAGxBA,EAAO,MAAM,UAAYuD,CAAa,cAG9BC,GACRC,EACAC,EAAc,CAEV,GAAA3C,IAAUO,IACdmC,EAAI,MAAM,UAAY,SACtBA,EAAI,iBAAiB,QAASL,CAAM,EAE/B,EAAAM,EAAO,KAAI,GAChB,OAAAN,EAAM,CAAG,OAAQK,CAAG,CAAA,GAGnB,YAAeA,EAAI,oBAAoB,QAASL,CAAM,+UAiB1CtB,EAAE6B,wFAiBF7B,EAAE6B,wFAiBF7B,EAAE6B,wFAkCH7B,EAAE6B,ypBA9NR/C,IAAU,MAAM4B,EAAA,EAAA5B,EAAQ,EAAE,uBAFvBkB,GAAMf,IAAUO,GAAa8B,EAAS,CAAA,OAAQtB,CAAE,CAAA,mBAsChDQ,GAAa"}