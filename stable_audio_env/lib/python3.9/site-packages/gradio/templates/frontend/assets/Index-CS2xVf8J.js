const{SvelteComponent:m,append:g,attr:r,create_slot:b,detach:h,element:f,flush:d,get_all_dirty_from_scope:v,get_slot_changes:w,init:p,insert:y,safe_not_equal:j,set_style:a,toggle_class:c,transition_in:k,transition_out:x,update_slot_base:q}=window.__gradio__svelte__internal;function C(_){let e,s,u,n;const o=_[4].default,i=b(o,_,_[3],null);return{c(){e=f("div"),s=f("div"),i&&i.c(),r(s,"class","styler svelte-iyf88w"),a(s,"--block-radius","0px"),a(s,"--block-border-width","0px"),a(s,"--layout-gap","1px"),a(s,"--form-gap-width","1px"),a(s,"--button-border-width","0px"),a(s,"--button-large-radius","0px"),a(s,"--button-small-radius","0px"),r(e,"id",_[0]),r(e,"class",u="gr-group "+_[1].join(" ")+" svelte-iyf88w"),c(e,"hide",!_[2])},m(t,l){y(t,e,l),g(e,s),i&&i.m(s,null),n=!0},p(t,[l]){i&&i.p&&(!n||l&8)&&q(i,o,t,t[3],n?w(o,t[3],l,null):v(t[3]),null),(!n||l&1)&&r(e,"id",t[0]),(!n||l&2&&u!==(u="gr-group "+t[1].join(" ")+" svelte-iyf88w"))&&r(e,"class",u),(!n||l&6)&&c(e,"hide",!t[2])},i(t){n||(k(i,t),n=!0)},o(t){x(i,t),n=!1},d(t){t&&h(e),i&&i.d(t)}}}function I(_,e,s){let{$$slots:u={},$$scope:n}=e,{elem_id:o=""}=e,{elem_classes:i=[]}=e,{visible:t=!0}=e;return _.$$set=l=>{"elem_id"in l&&s(0,o=l.elem_id),"elem_classes"in l&&s(1,i=l.elem_classes),"visible"in l&&s(2,t=l.visible),"$$scope"in l&&s(3,n=l.$$scope)},[o,i,t,n,u]}class S extends m{constructor(e){super(),p(this,e,I,C,j,{elem_id:0,elem_classes:1,visible:2})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),d()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),d()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),d()}}export{S as default};
//# sourceMappingURL=Index-CS2xVf8J.js.map
