{"version": 3, "file": "ModifyUpload-CEEIIKhx.js", "sources": ["../../../../js/icons/src/Edit.svelte", "../../../../js/upload/src/ModifyUpload.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-edit-2\"\n>\n\t<path d=\"M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { IconButton } from \"@gradio/atoms\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { Edit, Clear, Undo, Download } from \"@gradio/icons\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let editable = false;\n\texport let undoable = false;\n\texport let download: string | null = null;\n\texport let absolute = true;\n\texport let i18n: I18nFormatter;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tedit?: never;\n\t\tclear?: never;\n\t\tundo?: never;\n\t}>();\n</script>\n\n<div\n\tclass:not-absolute={!absolute}\n\tstyle:position={absolute ? \"absolute\" : \"static\"}\n>\n\t{#if editable}\n\t\t<IconButton\n\t\t\tIcon={Edit}\n\t\t\tlabel={i18n(\"common.edit\")}\n\t\t\ton:click={() => dispatch(\"edit\")}\n\t\t/>\n\t{/if}\n\n\t{#if undoable}\n\t\t<IconButton\n\t\t\tIcon={Undo}\n\t\t\tlabel={i18n(\"common.undo\")}\n\t\t\ton:click={() => dispatch(\"undo\")}\n\t\t/>\n\t{/if}\n\n\t{#if download}\n\t\t<DownloadLink href={download} download>\n\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t</DownloadLink>\n\t{/if}\n\n\t<IconButton\n\t\tIcon={Clear}\n\t\tlabel={i18n(\"common.clear\")}\n\t\ton:click={(event) => {\n\t\t\tdispatch(\"clear\");\n\t\t\tevent.stopPropagation();\n\t\t}}\n\t/>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.not-absolute {\n\t\tmargin: var(--size-1);\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "createEventDispatcher", "Edit", "ctx", "dirty", "iconbutton_changes", "Undo", "Download", "create_if_block_2", "create_if_block_1", "create_if_block", "Clear", "div", "editable", "$$props", "undoable", "download", "absolute", "i18n", "dispatch", "click_handler", "click_handler_1", "event"], "mappings": "yxBAAAA,EAaKC,EAAAC,EAAAC,CAAA,EADJC,EAAmEF,EAAAG,CAAA,gYCN1D,CAAA,sBAAAC,UAAqC,6EAqBtCC,EACC,MAAAC,KAAK,aAAa,wFAAlBC,EAAA,KAAAC,EAAA,MAAAF,KAAK,aAAa,+IAOnBG,EACC,MAAAH,KAAK,aAAa,wFAAlBC,EAAA,KAAAC,EAAA,MAAAF,KAAK,aAAa,+IAMNA,EAAQ,CAAA,mIAARA,EAAQ,CAAA,kLACTI,EAAiB,MAAAJ,KAAK,iBAAiB,oEAAtBC,EAAA,KAAAC,EAAA,MAAAF,KAAK,iBAAiB,6HAlBtDA,EAAQ,CAAA,GAAAK,EAAAL,CAAA,IAQRA,EAAQ,CAAA,GAAAM,EAAAN,CAAA,IAQRA,EAAQ,CAAA,GAAAO,EAAAP,CAAA,8BAONQ,EACC,MAAAR,KAAK,cAAc,yJA3BNA,EAAQ,CAAA,CAAA,iBACbA,EAAQ,CAAA,EAAG,WAAa,QAAQ,UAFjDR,EAkCKC,EAAAgB,EAAAd,CAAA,+FA9BCK,EAAQ,CAAA,6FAQRA,EAAQ,CAAA,6FAQRA,EAAQ,CAAA,wGAQLC,EAAA,KAAAC,EAAA,MAAAF,KAAK,cAAc,4CA3BNA,EAAQ,CAAA,CAAA,sBACbA,EAAQ,CAAA,EAAG,WAAa,QAAQ,oKAfrC,GAAA,CAAA,SAAAU,EAAW,EAAK,EAAAC,EAChB,CAAA,SAAAC,EAAW,EAAK,EAAAD,EAChB,CAAA,SAAAE,EAA0B,IAAI,EAAAF,EAC9B,CAAA,SAAAG,EAAW,EAAI,EAAAH,GACf,KAAAI,CAAmB,EAAAJ,EAExB,MAAAK,EAAWlB,IAeCmB,EAAA,IAAAD,EAAS,MAAM,EAQfE,EAAA,IAAAF,EAAS,MAAM,IAarBG,GAAK,CACfH,EAAS,OAAO,EAChBG,EAAM,gBAAe"}