import{T as v}from"./Tabs-C0yBUI0Z.js";import{a as O}from"./Tabs-C0yBUI0Z.js";import"./index-BQPjLIsY.js";import"./svelte/svelte.js";const{SvelteComponent:w,add_flush_callback:T,bind:k,binding_callbacks:p,create_component:S,create_slot:q,destroy_component:A,flush:d,get_all_dirty_from_scope:B,get_slot_changes:C,init:D,mount_component:E,safe_not_equal:I,transition_in:f,transition_out:m,update_slot_base:$}=window.__gradio__svelte__internal,{createEventDispatcher:j}=window.__gradio__svelte__internal;function z(t){let e;const a=t[5].default,s=q(a,t,t[9],null);return{c(){s&&s.c()},m(i,c){s&&s.m(i,c),e=!0},p(i,c){s&&s.p&&(!e||c&512)&&$(s,a,i,i[9],e?C(a,i[9],c,null):B(i[9]),null)},i(i){e||(f(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){s&&s.d(i)}}}function F(t){let e,a,s;function i(l){t[6](l)}let c={visible:t[1],elem_id:t[2],elem_classes:t[3],$$slots:{default:[z]},$$scope:{ctx:t}};return t[0]!==void 0&&(c.selected=t[0]),e=new v({props:c}),p.push(()=>k(e,"selected",i)),e.$on("change",t[7]),e.$on("select",t[8]),{c(){S(e.$$.fragment)},m(l,_){E(e,l,_),s=!0},p(l,[_]){const o={};_&2&&(o.visible=l[1]),_&4&&(o.elem_id=l[2]),_&8&&(o.elem_classes=l[3]),_&512&&(o.$$scope={dirty:_,ctx:l}),!a&&_&1&&(a=!0,o.selected=l[0],T(()=>a=!1)),e.$set(o)},i(l){s||(f(e.$$.fragment,l),s=!0)},o(l){m(e.$$.fragment,l),s=!1},d(l){A(e,l)}}}function G(t,e,a){let{$$slots:s={},$$scope:i}=e;const c=j();let{visible:l=!0}=e,{elem_id:_=""}=e,{elem_classes:o=[]}=e,{selected:r}=e,{gradio:u}=e;function g(n){r=n,a(0,r)}const h=()=>u.dispatch("change"),b=n=>u.dispatch("select",n.detail);return t.$$set=n=>{"visible"in n&&a(1,l=n.visible),"elem_id"in n&&a(2,_=n.elem_id),"elem_classes"in n&&a(3,o=n.elem_classes),"selected"in n&&a(0,r=n.selected),"gradio"in n&&a(4,u=n.gradio),"$$scope"in n&&a(9,i=n.$$scope)},t.$$.update=()=>{t.$$.dirty&1&&c("prop_change",{selected:r})},[r,l,_,o,u,s,g,h,b,i]}class L extends w{constructor(e){super(),D(this,e,G,F,I,{visible:1,elem_id:2,elem_classes:3,selected:0,gradio:4})}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),d()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),d()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),d()}get selected(){return this.$$.ctx[0]}set selected(e){this.$$set({selected:e}),d()}get gradio(){return this.$$.ctx[4]}set gradio(e){this.$$set({gradio:e}),d()}}export{O as TABS,L as default};
//# sourceMappingURL=Index-BfLZDIPR.js.map
