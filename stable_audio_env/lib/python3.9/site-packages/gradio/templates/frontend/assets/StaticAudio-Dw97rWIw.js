import{I as Vt}from"./Index-DB1XLvMK.js";import{f as He,u as Nt}from"./Blocks-CyfcXtBq.js";import{B as jt}from"./BlockLabel-BlSr62f_.js";import{E as zt}from"./Empty-BgF7sXBn.js";import{S as qt}from"./ShareButton-DRfMJDgB.js";import{D as Bt}from"./Download-DVtk-Jv3.js";import{M as nt}from"./Music-CDm0RGMk.js";import{a as It,P as Ut,T as xt}from"./Trim-UKwaW4UI.js";import{U as Ft}from"./Undo-CpmTQw3B.js";import{r as ht}from"./file-url-SIRImsEF.js";import{D as Xt}from"./DownloadLink-CHpWw1Ex.js";const{SvelteComponent:Gt,append:Zt,attr:x,detach:Yt,init:Jt,insert:Kt,noop:Ue,safe_not_equal:Qt,svg_element:ft}=window.__gradio__svelte__internal;function ei(s){let e,t;return{c(){e=ft("svg"),t=ft("path"),x(t,"stroke","currentColor"),x(t,"stroke-width","1.5"),x(t,"stroke-linecap","round"),x(t,"stroke-linejoin","round"),x(t,"d","M21.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.889-5.813a.6.6 0 0 1 0-.966l7.89-5.813ZM10.044 5.704a.6.6 0 0 1 .956.483v11.626a.6.6 0 0 1-.956.483l-7.888-5.813a.6.6 0 0 1 0-.966l7.888-5.813Z"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","24px"),x(e,"height","24px"),x(e,"fill","currentColor"),x(e,"stroke-width","1.5"),x(e,"viewBox","0 0 24 24"),x(e,"color","currentColor")},m(i,n){Kt(i,e,n),Zt(e,t)},p:Ue,i:Ue,o:Ue,d(i){i&&Yt(e)}}}class ti extends Gt{constructor(e){super(),Jt(this,e,null,ei,Qt,{})}}const{SvelteComponent:ii,append:ni,attr:F,detach:si,init:ri,insert:oi,noop:xe,safe_not_equal:li,svg_element:mt}=window.__gradio__svelte__internal;function ai(s){let e,t;return{c(){e=mt("svg"),t=mt("path"),F(t,"stroke","currentColor"),F(t,"stroke-width","1.5"),F(t,"stroke-linecap","round"),F(t,"stroke-linejoin","round"),F(t,"d","M2.956 5.704A.6.6 0 0 0 2 6.187v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813ZM13.956 5.704a.6.6 0 0 0-.956.483v11.626a.6.6 0 0 0 .956.483l7.889-5.813a.6.6 0 0 0 0-.966l-7.89-5.813Z"),F(e,"xmlns","http://www.w3.org/2000/svg"),F(e,"width","24px"),F(e,"height","24px"),F(e,"fill","currentColor"),F(e,"stroke-width","1.5"),F(e,"viewBox","0 0 24 24"),F(e,"color","currentColor")},m(i,n){oi(i,e,n),ni(e,t)},p:xe,i:xe,o:xe,d(i){i&&si(e)}}}class ui extends ii{constructor(e){super(),ri(this,e,null,ai,li,{})}}const{SvelteComponent:di,append:Ae,attr:q,detach:ci,init:hi,insert:fi,noop:Fe,safe_not_equal:mi,svg_element:We,text:_i}=window.__gradio__svelte__internal;function pi(s){let e,t,i,n,r;return{c(){e=We("svg"),t=We("title"),i=_i("Low volume"),n=We("path"),r=We("path"),q(n,"d","M19.5 7.5C19.5 7.5 21 9 21 11.5C21 14 19.5 15.5 19.5 15.5"),q(n,"stroke-width","1.5"),q(n,"stroke-linecap","round"),q(n,"stroke-linejoin","round"),q(r,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),q(r,"stroke-width","1.5"),q(e,"width","100%"),q(e,"height","100%"),q(e,"viewBox","0 0 24 24"),q(e,"stroke-width","1.5"),q(e,"fill","none"),q(e,"xmlns","http://www.w3.org/2000/svg"),q(e,"stroke","currentColor"),q(e,"color","currentColor")},m(o,u){fi(o,e,u),Ae(e,t),Ae(t,i),Ae(e,n),Ae(e,r)},p:Fe,i:Fe,o:Fe,d(o){o&&ci(e)}}}class gi extends di{constructor(e){super(),hi(this,e,null,pi,mi,{})}}const{SvelteComponent:vi,append:Ee,attr:W,detach:bi,init:wi,insert:yi,noop:Xe,safe_not_equal:ki,svg_element:Se,text:Ci}=window.__gradio__svelte__internal;function Ei(s){let e,t,i,n,r,o;return{c(){e=Se("svg"),t=Se("title"),i=Ci("High volume"),n=Se("path"),r=Se("path"),o=Se("path"),W(n,"d","M1 13.8571V10.1429C1 9.03829 1.89543 8.14286 3 8.14286H5.9C6.09569 8.14286 6.28708 8.08544 6.45046 7.97772L12.4495 4.02228C13.1144 3.5839 14 4.06075 14 4.85714V19.1429C14 19.9392 13.1144 20.4161 12.4495 19.9777L6.45046 16.0223C6.28708 15.9146 6.09569 15.8571 5.9 15.8571H3C1.89543 15.8571 1 14.9617 1 13.8571Z"),W(n,"stroke-width","1.5"),W(r,"d","M17.5 7.5C17.5 7.5 19 9 19 11.5C19 14 17.5 15.5 17.5 15.5"),W(r,"stroke-width","1.5"),W(r,"stroke-linecap","round"),W(r,"stroke-linejoin","round"),W(o,"d","M20.5 4.5C20.5 4.5 23 7 23 11.5C23 16 20.5 18.5 20.5 18.5"),W(o,"stroke-width","1.5"),W(o,"stroke-linecap","round"),W(o,"stroke-linejoin","round"),W(e,"width","100%"),W(e,"height","100%"),W(e,"viewBox","0 0 24 24"),W(e,"stroke-width","1.5"),W(e,"fill","none"),W(e,"stroke","currentColor"),W(e,"xmlns","http://www.w3.org/2000/svg"),W(e,"color","currentColor")},m(u,a){yi(u,e,a),Ee(e,t),Ee(t,i),Ee(e,n),Ee(e,r),Ee(e,o)},p:Xe,i:Xe,o:Xe,d(u){u&&bi(e)}}}class Si extends vi{constructor(e){super(),wi(this,e,null,Ei,ki,{})}}const{SvelteComponent:Li,append:ee,attr:A,detach:$i,init:Mi,insert:Di,noop:Ge,safe_not_equal:Ri,svg_element:te,text:Pi}=window.__gradio__svelte__internal;function Ti(s){let e,t,i,n,r,o,u,a,d;return{c(){e=te("svg"),t=te("title"),i=Pi("Muted volume"),n=te("g"),r=te("path"),o=te("path"),u=te("defs"),a=te("clipPath"),d=te("rect"),A(r,"d","M18 14L20.0005 12M22 10L20.0005 12M20.0005 12L18 10M20.0005 12L22 14"),A(r,"stroke-width","1.5"),A(r,"stroke-linecap","round"),A(r,"stroke-linejoin","round"),A(o,"d","M2 13.8571V10.1429C2 9.03829 2.89543 8.14286 4 8.14286H6.9C7.09569 8.14286 7.28708 8.08544 7.45046 7.97772L13.4495 4.02228C14.1144 3.5839 15 4.06075 15 4.85714V19.1429C15 19.9392 14.1144 20.4161 13.4495 19.9777L7.45046 16.0223C7.28708 15.9146 7.09569 15.8571 6.9 15.8571H4C2.89543 15.8571 2 14.9617 2 13.8571Z"),A(o,"stroke-width","1.5"),A(n,"clip-path","url(#clip0_3173_16686)"),A(d,"width","24"),A(d,"height","24"),A(d,"fill","white"),A(a,"id","clip0_3173_16686"),A(e,"width","100%"),A(e,"height","100%"),A(e,"viewBox","0 0 24 24"),A(e,"stroke-width","1.5"),A(e,"fill","none"),A(e,"xmlns","http://www.w3.org/2000/svg"),A(e,"stroke","currentColor"),A(e,"color","currentColor")},m(l,c){Di(l,e,c),ee(e,t),ee(t,i),ee(e,n),ee(n,r),ee(n,o),ee(e,u),ee(u,a),ee(a,d)},p:Ge,i:Ge,o:Ge,d(l){l&&$i(e)}}}class Ai extends Li{constructor(e){super(),Mi(this,e,null,Ti,Ri,{})}}var Wi=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function u(l){try{d(i.next(l))}catch(c){o(c)}}function a(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(u,a)}d((i=i.apply(s,e||[])).next())})};function zi(s,e){return Wi(this,void 0,void 0,function*(){const t=new AudioContext({sampleRate:e});return t.decodeAudioData(s).finally(()=>t.close())})}function Oi(s){const e=s[0];if(e.some(t=>t>1||t<-1)){const t=e.length;let i=0;for(let n=0;n<t;n++){const r=Math.abs(e[n]);r>i&&(i=r)}for(const n of s)for(let r=0;r<t;r++)n[r]/=i}return s}function Hi(s,e){return typeof s[0]=="number"&&(s=[s]),Oi(s),{duration:e,length:s[0].length,sampleRate:s[0].length/e,numberOfChannels:s.length,getChannelData:t=>s?.[t],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const Ze={decode:zi,createBuffer:Hi};var _t=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function u(l){try{d(i.next(l))}catch(c){o(c)}}function a(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(u,a)}d((i=i.apply(s,e||[])).next())})};function Vi(s,e,t){var i,n;return _t(this,void 0,void 0,function*(){const r=yield fetch(s,t);{const o=(i=r.clone().body)===null||i===void 0?void 0:i.getReader(),u=Number((n=r.headers)===null||n===void 0?void 0:n.get("Content-Length"));let a=0;const d=(l,c)=>_t(this,void 0,void 0,function*(){if(l)return;a+=c?.length||0;const _=Math.round(a/u*100);return e(_),o?.read().then(({done:m,value:g})=>d(m,g))});o?.read().then(({done:l,value:c})=>d(l,c))}return r.blob()})}const Ni={fetchBlob:Vi};class je{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class ji extends je{constructor(e){super(),this.isExternalMedia=!1,e.media?(this.media=e.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),e.mediaControls&&(this.media.controls=!0),e.autoplay&&(this.media.autoplay=!0),e.playbackRate!=null&&this.onceMediaEvent("canplay",()=>{e.playbackRate!=null&&(this.media.playbackRate=e.playbackRate)})}onMediaEvent(e,t,i){return this.media.addEventListener(e,t,i),()=>this.media.removeEventListener(e,t)}onceMediaEvent(e,t){return this.onMediaEvent(e,t,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const e=this.getSrc();e.startsWith("blob:")&&URL.revokeObjectURL(e)}setSrc(e,t){if(this.getSrc()===e)return;this.revokeSrc();const n=t instanceof Blob?URL.createObjectURL(t):e;this.media.src=n,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(e){this.media=e}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(e){this.media.currentTime=e}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(e){this.media.volume=e}getMuted(){return this.media.muted}setMuted(e){this.media.muted=e}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(e,t){t!=null&&(this.media.preservesPitch=t),this.media.playbackRate=e}getMediaElement(){return this.media}setSinkId(e){return this.media.setSinkId(e)}}function qi(s,e,t,i,n=5){let r=()=>{};if(!s)return r;const o=u=>{if(u.button===2)return;u.preventDefault(),u.stopPropagation(),s.style.touchAction="none";let a=u.clientX,d=u.clientY,l=!1;const c=g=>{g.preventDefault(),g.stopPropagation();const C=g.clientX,E=g.clientY;if(l||Math.abs(C-a)>=n||Math.abs(E-d)>=n){const{left:b,top:w}=s.getBoundingClientRect();l||(l=!0,t?.(a-b,d-w)),e(C-a,E-d,C-b,E-w),a=C,d=E}},_=g=>{l&&(g.preventDefault(),g.stopPropagation())},m=()=>{s.style.touchAction="",l&&i?.(),r()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",m),document.addEventListener("pointerleave",m),document.addEventListener("click",_,!0),r=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",m),document.removeEventListener("pointerleave",m),setTimeout(()=>{document.removeEventListener("click",_,!0)},10)}};return s.addEventListener("pointerdown",o),()=>{r(),s.removeEventListener("pointerdown",o)}}class qe extends je{constructor(e,t){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=e;const i=this.parentFromOptionsContainer(e.container);this.parent=i;const[n,r]=this.initHtml();i.appendChild(n),this.container=n,this.scrollContainer=r.querySelector(".scroll"),this.wrapper=r.querySelector(".wrapper"),this.canvasWrapper=r.querySelector(".canvases"),this.progressWrapper=r.querySelector(".progress"),this.cursor=r.querySelector(".cursor"),t&&r.appendChild(t),this.initEvents()}parentFromOptionsContainer(e){let t;if(typeof e=="string"?t=document.querySelector(e):e instanceof HTMLElement&&(t=e),!t)throw new Error("Container not found");return t}initEvents(){const e=i=>{const n=this.wrapper.getBoundingClientRect(),r=i.clientX-n.left,o=i.clientX-n.left,u=r/n.width,a=o/n.height;return[u,a]};this.wrapper.addEventListener("click",i=>{const[n,r]=e(i);this.emit("click",n,r)}),this.wrapper.addEventListener("dblclick",i=>{const[n,r]=e(i);this.emit("dblclick",n,r)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:i,scrollWidth:n,clientWidth:r}=this.scrollContainer,o=i/n,u=(i+r)/n;this.emit("scroll",o,u)});const t=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{t(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){qi(this.wrapper,(e,t,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height==="auto"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const e=document.createElement("div"),t=e.attachShadow({mode:"open"});return t.innerHTML=`
      <style>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight()}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[e,t]}setOptions(e){if(this.options.container!==e.container){const t=this.parentFromOptionsContainer(e.container);t.appendChild(this.container),this.parent=t}e.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=e,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var e;this.container.remove(),(e=this.resizeObserver)===null||e===void 0||e.disconnect()}createDelay(e=10){const t={};return this.timeouts.push(t),i=>{t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(i,e)}}convertColorValues(e){if(!Array.isArray(e))return e||"";if(e.length<2)return e[0]||"";const t=document.createElement("canvas"),n=t.getContext("2d").createLinearGradient(0,0,0,t.height),r=1/(e.length-1);return e.forEach((o,u)=>{const a=u*r;n.addColorStop(a,o)}),n}renderBarWaveform(e,t,i,n){const r=e[0],o=e[1]||e[0],u=r.length,{width:a,height:d}=i.canvas,l=d/2,c=window.devicePixelRatio||1,_=t.barWidth?t.barWidth*c:1,m=t.barGap?t.barGap*c:t.barWidth?_/2:0,g=t.barRadius||0,C=a/(_+m)/u,E=g&&"roundRect"in i?"roundRect":"rect";i.beginPath();let b=0,w=0,L=0;for(let k=0;k<=u;k++){const h=Math.round(k*C);if(h>b){const S=Math.round(w*l*n),M=Math.round(L*l*n),J=S+M||1;let I=l-S;t.barAlign==="top"?I=0:t.barAlign==="bottom"&&(I=d-J),i[E](b*(_+m),I,_,J,g),b=h,w=0,L=0}const $=Math.abs(r[k]||0),p=Math.abs(o[k]||0);$>w&&(w=$),p>L&&(L=p)}i.fill(),i.closePath()}renderLineWaveform(e,t,i,n){const r=o=>{const u=e[o]||e[0],a=u.length,{height:d}=i.canvas,l=d/2,c=i.canvas.width/a;i.moveTo(0,l);let _=0,m=0;for(let g=0;g<=a;g++){const C=Math.round(g*c);if(C>_){const b=Math.round(m*l*n)||1,w=l+b*(o===0?-1:1);i.lineTo(_,w),_=C,m=0}const E=Math.abs(u[g]||0);E>m&&(m=E)}i.lineTo(_,l)};i.beginPath(),r(0),r(1),i.fill(),i.closePath()}renderWaveform(e,t,i){if(i.fillStyle=this.convertColorValues(t.waveColor),t.renderFunction){t.renderFunction(e,i);return}let n=t.barHeight||1;if(t.normalize){const r=Array.from(e[0]).reduce((o,u)=>Math.max(o,Math.abs(u)),0);n=r?1/r:1}if(t.barWidth||t.barGap||t.barAlign){this.renderBarWaveform(e,t,i,n);return}this.renderLineWaveform(e,t,i,n)}renderSingleCanvas(e,t,i,n,r,o,u,a){const d=window.devicePixelRatio||1,l=document.createElement("canvas"),c=e[0].length;l.width=Math.round(i*(o-r)/c),l.height=n*d,l.style.width=`${Math.floor(l.width/d)}px`,l.style.height=`${n}px`,l.style.left=`${Math.floor(r*i/d/c)}px`,u.appendChild(l);const _=l.getContext("2d");if(this.renderWaveform(e.map(m=>m.slice(r,o)),t,_),l.width>0&&l.height>0){const m=l.cloneNode(),g=m.getContext("2d");g.drawImage(l,0,0),g.globalCompositeOperation="source-in",g.fillStyle=this.convertColorValues(t.progressColor),g.fillRect(0,0,l.width,l.height),a.appendChild(m)}}renderChannel(e,t,i){const n=document.createElement("div"),r=this.getHeight();n.style.height=`${r}px`,this.canvasWrapper.style.minHeight=`${r}px`,this.canvasWrapper.appendChild(n);const o=n.cloneNode();this.progressWrapper.appendChild(o);const{scrollLeft:u,scrollWidth:a,clientWidth:d}=this.scrollContainer,l=e[0].length,c=l/a;let _=Math.min(qe.MAX_CANVAS_WIDTH,d);if(t.barWidth||t.barGap){const h=t.barWidth||.5,$=t.barGap||h/2,p=h+$;_%p!==0&&(_=Math.floor(_/p)*p)}const m=Math.floor(Math.abs(u)*c),g=Math.floor(m+_*c),C=g-m,E=(h,$)=>{this.renderSingleCanvas(e,t,i,r,Math.max(0,h),Math.min($,l),n,o)},b=this.createDelay(),w=this.createDelay(),L=(h,$)=>{E(h,$),h>0&&b(()=>{L(h-C,$-C)})},k=(h,$)=>{E(h,$),$<l&&w(()=>{k(h+C,$+C)})};L(m,g),g<l&&k(g,g+C)}render(e){this.timeouts.forEach(u=>u.timeout&&clearTimeout(u.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.wrapper.style.width="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const t=window.devicePixelRatio||1,i=this.scrollContainer.clientWidth,n=Math.ceil(e.duration*(this.options.minPxPerSec||0));this.isScrolling=n>i;const r=this.options.fillParent&&!this.isScrolling,o=(r?i:n)*t;if(this.wrapper.style.width=r?"100%":`${n}px`,this.scrollContainer.style.overflowX=this.isScrolling?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let u=0;u<e.numberOfChannels;u++){const a=Object.assign(Object.assign({},this.options),this.options.splitChannels[u]);this.renderChannel([e.getChannelData(u)],a,o)}else{const u=[e.getChannelData(0)];e.numberOfChannels>1&&u.push(e.getChannelData(1)),this.renderChannel(u,this.options,o)}this.audioData=e,this.emit("render")}reRender(){if(!this.audioData)return;const e=this.progressWrapper.clientWidth;this.render(this.audioData);const t=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=t-e}zoom(e){this.options.minPxPerSec=e,this.reRender()}scrollIntoView(e,t=!1){const{clientWidth:i,scrollLeft:n,scrollWidth:r}=this.scrollContainer,o=r*e,u=i/2,a=t&&this.options.autoCenter&&!this.isDragging?u:i;if(o>n+a||o<n)if(this.options.autoCenter&&!this.isDragging){const d=u/20;o-(n+u)>=d&&o<n+i?this.scrollContainer.scrollLeft+=d:this.scrollContainer.scrollLeft=o-u}else this.isDragging?this.scrollContainer.scrollLeft=o<n?o-10:o-i+10:this.scrollContainer.scrollLeft=o;{const{scrollLeft:d}=this.scrollContainer,l=d/r,c=(d+i)/r;this.emit("scroll",l,c)}}renderProgress(e,t){if(isNaN(e))return;const i=e*100;this.canvasWrapper.style.clipPath=`polygon(${i}% 0, 100% 0, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.marginLeft=Math.round(i)===100?`-${this.options.cursorWidth}px`:"",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(e,t)}}qe.MAX_CANVAS_WIDTH=4e3;class Bi extends je{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var Ye=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function u(l){try{d(i.next(l))}catch(c){o(c)}}function a(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(u,a)}d((i=i.apply(s,e||[])).next())})};class Ii extends je{constructor(e=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.audioContext=e,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return Ye(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(e){this.currentSrc=e,fetch(e).then(t=>t.arrayBuffer()).then(t=>this.audioContext.decodeAudioData(t)).then(t=>{this.buffer=t,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play()})}_play(){var e;this.paused&&(this.paused=!1,(e=this.bufferNode)===null||e===void 0||e.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))})}_pause(){var e;this.paused||(this.paused=!0,(e=this.bufferNode)===null||e===void 0||e.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return Ye(this,void 0,void 0,function*(){this._play(),this.emit("play")})}pause(){this._pause(),this.emit("pause")}setSinkId(e){return Ye(this,void 0,void 0,function*(){return this.audioContext.setSinkId(e)})}get playbackRate(){var e,t;return(t=(e=this.bufferNode)===null||e===void 0?void 0:e.playbackRate.value)!==null&&t!==void 0?t:1}set playbackRate(e){this.bufferNode&&(this.bufferNode.playbackRate.value=e)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(e){this.emit("seeking"),this.paused?this.playedDuration=e:(this._pause(),this.playedDuration=e,this._play()),this.emit("timeupdate")}get duration(){var e;return((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}get volume(){return this.gainNode.gain.value}set volume(e){this.gainNode.gain.value=e,this.emit("volumechange")}get muted(){return this._muted}set muted(e){this._muted!==e&&(this._muted=e,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var ge=function(s,e,t,i){function n(r){return r instanceof t?r:new t(function(o){o(r)})}return new(t||(t=Promise))(function(r,o){function u(l){try{d(i.next(l))}catch(c){o(c)}}function a(l){try{d(i.throw(l))}catch(c){o(c)}}function d(l){l.done?r(l.value):n(l.value).then(u,a)}d((i=i.apply(s,e||[])).next())})};const Ui={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class st extends ji{static create(e){return new st(e)}constructor(e){const t=e.media||(e.backend==="WebAudio"?new Ii:void 0);super({media:t,mediaControls:e.mediaControls,autoplay:e.autoplay,playbackRate:e.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},Ui,e),this.timer=new Bi;const i=t?void 0:this.getMediaElement();this.renderer=new qe(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const n=this.options.url||this.getSrc();n?this.load(n,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),!0),this.emit("timeupdate",e),this.emit("audioprocess",e)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const e=this.getCurrentTime();this.renderer.renderProgress(e/this.getDuration(),this.isPlaying()),this.emit("timeupdate",e)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop()}),this.onMediaEvent("emptied",()=>{this.timer.stop()}),this.onMediaEvent("ended",()=>{this.emit("finish")}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(e,t)=>{this.options.interact&&(this.seekTo(e),this.emit("interaction",e*this.getDuration()),this.emit("click",e,t))}),this.renderer.on("dblclick",(e,t)=>{this.emit("dblclick",e,t)}),this.renderer.on("scroll",(e,t)=>{const i=this.getDuration();this.emit("scroll",e*i,t*i)}),this.renderer.on("render",()=>{this.emit("redraw")}));{let e;this.subscriptions.push(this.renderer.on("drag",t=>{this.options.interact&&(this.renderer.renderProgress(t),clearTimeout(e),e=setTimeout(()=>{this.seekTo(t)},this.isPlaying()?0:200),this.emit("interaction",t*this.getDuration()),this.emit("drag",t))}))}}initPlugins(){var e;!((e=this.options.plugins)===null||e===void 0)&&e.length&&this.options.plugins.forEach(t=>{this.registerPlugin(t)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(e=>e()),this.mediaSubscriptions=[]}setOptions(e){this.options=Object.assign({},this.options,e),this.renderer.setOptions(this.options),e.audioRate&&this.setPlaybackRate(e.audioRate),e.mediaControls!=null&&(this.getMediaElement().controls=e.mediaControls)}registerPlugin(e){return e.init(this),this.plugins.push(e),this.subscriptions.push(e.once("destroy",()=>{this.plugins=this.plugins.filter(t=>t!==e)})),e}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return ge(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=Ze.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return ge(this,void 0,void 0,function*(){this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(e,t,i,n){return ge(this,void 0,void 0,function*(){if(this.emit("load",e),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!t&&!i){const r=o=>this.emit("loading",o);t=yield Ni.fetchBlob(e,r,this.options.fetchParams)}if(this.setSrc(e,t),n=(yield Promise.resolve(n||this.getDuration()))||(yield new Promise(r=>{this.onceMediaEvent("loadedmetadata",()=>r(this.getDuration()))}))||(yield Promise.resolve(0)),i)this.decodedData=Ze.createBuffer(i,n);else if(t){const r=yield t.arrayBuffer();this.decodedData=yield Ze.decode(r,this.options.sampleRate)}this.renderDecoded(),this.emit("ready",this.getDuration())})}load(e,t,i){return ge(this,void 0,void 0,function*(){yield this.loadAudio(e,void 0,t,i)})}loadBlob(e,t,i){return ge(this,void 0,void 0,function*(){yield this.loadAudio("blob",e,t,i)})}zoom(e){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(e),this.emit("zoom",e)}getDecodedData(){return this.decodedData}exportPeaks({channels:e=2,maxLength:t=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const n=Math.min(e,this.decodedData.numberOfChannels),r=[];for(let o=0;o<n;o++){const u=this.decodedData.getChannelData(o),a=[],d=Math.round(u.length/t);for(let l=0;l<t;l++){const c=u.slice(l*d,(l+1)*d),_=Math.max(...c);a.push(Math.round(_*i)/i)}r.push(a)}return r}getDuration(){let e=super.getDuration()||0;return(e===0||e===1/0)&&this.decodedData&&(e=this.decodedData.duration),e}toggleInteraction(e){this.options.interact=e}seekTo(e){const t=this.getDuration()*e;this.setTime(t)}playPause(){return ge(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(e){this.setTime(this.getCurrentTime()+e)}empty(){this.load("",[[0]],.001)}setMediaElement(e){this.unsubscribePlayerEvents(),super.setMediaElement(e),this.initPlayerEvents()}destroy(){this.emit("destroy"),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function xi(s){const e=s.numberOfChannels,t=s.length*e*2+44,i=new ArrayBuffer(t),n=new DataView(i);let r=0;const o=function(u,a,d){for(let l=0;l<d.length;l++)u.setUint8(a+l,d.charCodeAt(l))};o(n,r,"RIFF"),r+=4,n.setUint32(r,t-8,!0),r+=4,o(n,r,"WAVE"),r+=4,o(n,r,"fmt "),r+=4,n.setUint32(r,16,!0),r+=4,n.setUint16(r,1,!0),r+=2,n.setUint16(r,e,!0),r+=2,n.setUint32(r,s.sampleRate,!0),r+=4,n.setUint32(r,s.sampleRate*2*e,!0),r+=4,n.setUint16(r,e*2,!0),r+=2,n.setUint16(r,16,!0),r+=2,o(n,r,"data"),r+=4,n.setUint32(r,s.length*e*2,!0),r+=4;for(let u=0;u<s.length;u++)for(let a=0;a<e;a++){const d=Math.max(-1,Math.min(1,s.getChannelData(a)[u]));n.setInt16(r,d*32767,!0),r+=2}return new Uint8Array(i)}const Fi=async(s,e,t,i)=>{const n=new AudioContext({sampleRate:i||s.sampleRate}),r=s.numberOfChannels,o=i||s.sampleRate;let u=s.length,a=0;e&&t&&(a=Math.round(e*o),u=Math.round(t*o)-a);const d=n.createBuffer(r,u,o);for(let l=0;l<r;l++){const c=s.getChannelData(l),_=d.getChannelData(l);for(let m=0;m<u;m++)_[m]=c[a+m]}return xi(d)},pt=(s,e)=>{s&&s.skip(e)},we=(s,e)=>(e||(e=5),s/100*e||5);class Ot{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const n=()=>{this.removeEventListener(e,n),this.removeEventListener(e,t)};return this.addEventListener(e,n),n}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class Xi extends Ot{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}function Oe(s,e,t,i,n=5){let r=()=>{};if(!s)return r;const o=u=>{if(u.button===2)return;u.preventDefault(),u.stopPropagation(),s.style.touchAction="none";let a=u.clientX,d=u.clientY,l=!1;const c=g=>{g.preventDefault(),g.stopPropagation();const C=g.clientX,E=g.clientY;if(l||Math.abs(C-a)>=n||Math.abs(E-d)>=n){const{left:b,top:w}=s.getBoundingClientRect();l||(l=!0,t?.(a-b,d-w)),e(C-a,E-d,C-b,E-w),a=C,d=E}},_=g=>{l&&(g.preventDefault(),g.stopPropagation())},m=()=>{s.style.touchAction="",l&&i?.(),r()};document.addEventListener("pointermove",c),document.addEventListener("pointerup",m),document.addEventListener("pointerleave",m),document.addEventListener("click",_,!0),r=()=>{document.removeEventListener("pointermove",c),document.removeEventListener("pointerup",m),document.removeEventListener("pointerleave",m),setTimeout(()=>{document.removeEventListener("click",_,!0)},10)}};return s.addEventListener("pointerdown",o),()=>{r(),s.removeEventListener("pointerdown",o)}}class gt extends Ot{constructor(e,t,i=0){var n,r,o,u,a,d,l;super(),this.totalDuration=t,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.id=e.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(e.start),this.end=this.clampPosition((n=e.end)!==null&&n!==void 0?n:e.start),this.drag=(r=e.drag)===null||r===void 0||r,this.resize=(o=e.resize)===null||o===void 0||o,this.color=(u=e.color)!==null&&u!==void 0?u:"rgba(0, 0, 0, 0.1)",this.minLength=(a=e.minLength)!==null&&a!==void 0?a:this.minLength,this.maxLength=(d=e.maxLength)!==null&&d!==void 0?d:this.maxLength,this.channelIdx=(l=e.channelIdx)!==null&&l!==void 0?l:-1,this.element=this.initElement(),this.setContent(e.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(e){return Math.max(0,Math.min(this.totalDuration,e))}setPart(){const e=this.start===this.end;this.element.setAttribute("part",`${e?"marker":"region"} ${this.id}`)}addResizeHandles(e){const t=document.createElement("div");t.setAttribute("data-resize","left"),t.setAttribute("style",`
        position: absolute;
        z-index: 2;
        width: 6px;
        height: 100%;
        top: 0;
        left: 0;
        border-left: 2px solid rgba(0, 0, 0, 0.5);
        border-radius: 2px 0 0 2px;
        cursor: ew-resize;
        word-break: keep-all;
      `),t.setAttribute("part","region-handle region-handle-left");const i=t.cloneNode();i.setAttribute("data-resize","right"),i.style.left="",i.style.right="0",i.style.borderRight=i.style.borderLeft,i.style.borderLeft="",i.style.borderRadius="0 2px 2px 0",i.setAttribute("part","region-handle region-handle-right"),e.appendChild(t),e.appendChild(i),Oe(t,n=>this.onResize(n,"start"),()=>null,()=>this.onEndResizing(),1),Oe(i,n=>this.onResize(n,"end"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(e){const t=e.querySelector('[data-resize="left"]'),i=e.querySelector('[data-resize="right"]');t&&e.removeChild(t),i&&e.removeChild(i)}initElement(){const e=document.createElement("div"),t=this.start===this.end;let i=0,n=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(n=100/this.numberOfChannels,i=n*this.channelIdx),e.setAttribute("style",`
      position: absolute;
      top: ${i}%;
      height: ${n}%;
      background-color: ${t?"none":this.color};
      border-left: ${t?"2px solid "+this.color:"none"};
      border-radius: 2px;
      box-sizing: border-box;
      transition: background-color 0.2s ease;
      cursor: ${this.drag?"grab":"default"};
      pointer-events: all;
    `),!t&&this.resize&&this.addResizeHandles(e),e}renderPosition(){const e=this.start/this.totalDuration,t=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*e+"%",this.element.style.right=100*t+"%"}initMouseEvents(){const{element:e}=this;e&&(e.addEventListener("click",t=>this.emit("click",t)),e.addEventListener("mouseenter",t=>this.emit("over",t)),e.addEventListener("mouseleave",t=>this.emit("leave",t)),e.addEventListener("dblclick",t=>this.emit("dblclick",t)),Oe(e,t=>this.onMove(t),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor="grabbing")}onEndMoving(){this.drag&&(this.element.style.cursor="grab",this.emit("update-end"))}_onUpdate(e,t){if(!this.element.parentElement)return;const i=e/this.element.parentElement.clientWidth*this.totalDuration,n=t&&t!=="start"?this.start:this.start+i,r=t&&t!=="end"?this.end:this.end+i,o=r-n;n>=0&&r<=this.totalDuration&&n<=r&&o>=this.minLength&&o<=this.maxLength&&(this.start=n,this.end=r,this.renderPosition(),this.emit("update"))}onMove(e){this.drag&&this._onUpdate(e)}onResize(e,t){this.resize&&this._onUpdate(e,t)}onEndResizing(){this.resize&&this.emit("update-end")}_setTotalDuration(e){this.totalDuration=e,this.renderPosition()}play(){this.emit("play")}setContent(e){var t;if((t=this.content)===null||t===void 0||t.remove(),e){if(typeof e=="string"){this.content=document.createElement("div");const i=this.start===this.end;this.content.style.padding=`0.2em ${i?.2:.4}em`,this.content.textContent=e}else this.content=e;this.content.setAttribute("part","region-content"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(e){var t,i;if(e.color&&(this.color=e.color,this.element.style.backgroundColor=this.color),e.drag!==void 0&&(this.drag=e.drag,this.element.style.cursor=this.drag?"grab":"default"),e.start!==void 0||e.end!==void 0){const n=this.start===this.end;this.start=this.clampPosition((t=e.start)!==null&&t!==void 0?t:this.start),this.end=this.clampPosition((i=e.end)!==null&&i!==void 0?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(e.content&&this.setContent(e.content),e.id&&(this.id=e.id,this.setPart()),e.resize!==void 0&&e.resize!==this.resize){const n=this.start===this.end;this.resize=e.resize,this.resize&&!n?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit("remove"),this.element.remove(),this.element=null}}class rt extends Xi{constructor(e){super(e),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(e){return new rt(e)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let e=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",t=>{const i=this.regions.filter(n=>n.start<=t&&n.end>=t);i.forEach(n=>{e.includes(n)||this.emit("region-in",n)}),e.forEach(n=>{i.includes(n)||this.emit("region-out",n)}),e=i}))}initRegionsContainer(){const e=document.createElement("div");return e.setAttribute("style",`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: none;
    `),e}getRegions(){return this.regions}avoidOverlapping(e){if(!e.content)return;const t=e.content,i=t.getBoundingClientRect().left,n=e.element.scrollWidth,r=this.regions.filter(o=>{if(o===e||!o.content)return!1;const u=o.content.getBoundingClientRect().left,a=o.element.scrollWidth;return i<u+a&&u<i+n}).map(o=>{var u;return((u=o.content)===null||u===void 0?void 0:u.getBoundingClientRect().height)||0}).reduce((o,u)=>o+u,0);t.style.marginTop=`${r}px`}saveRegion(e){this.regionsContainer.appendChild(e.element),this.avoidOverlapping(e),this.regions.push(e);const t=[e.on("update-end",()=>{this.avoidOverlapping(e),this.emit("region-updated",e)}),e.on("play",()=>{var i,n;(i=this.wavesurfer)===null||i===void 0||i.play(),(n=this.wavesurfer)===null||n===void 0||n.setTime(e.start)}),e.on("click",i=>{this.emit("region-clicked",e,i)}),e.on("dblclick",i=>{this.emit("region-double-clicked",e,i)}),e.once("remove",()=>{t.forEach(i=>i()),this.regions=this.regions.filter(i=>i!==e)})];this.subscriptions.push(...t),this.emit("region-created",e)}addRegion(e){var t,i;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const n=this.wavesurfer.getDuration(),r=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getDecodedData())===null||i===void 0?void 0:i.numberOfChannels,o=new gt(e,n,r);return n?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once("ready",u=>{o._setTotalDuration(u),this.saveRegion(o)})),o}enableDragSelection(e){var t,i;const n=(i=(t=this.wavesurfer)===null||t===void 0?void 0:t.getWrapper())===null||i===void 0?void 0:i.querySelector("div");if(!n)return()=>{};let r=null,o=0;return Oe(n,(u,a,d)=>{r&&r._onUpdate(u,d>o?"end":"start")},u=>{var a,d;if(o=u,!this.wavesurfer)return;const l=this.wavesurfer.getDuration(),c=(d=(a=this.wavesurfer)===null||a===void 0?void 0:a.getDecodedData())===null||d===void 0?void 0:d.numberOfChannels,_=this.wavesurfer.getWrapper().clientWidth,m=u/_*l,g=(u+5)/_*l;r=new gt(Object.assign(Object.assign({},e),{start:m,end:g}),l,c),this.regionsContainer.appendChild(r.element)},()=>{r&&(this.saveRegion(r),r=null)})}clearRegions(){this.regions.forEach(e=>e.remove())}destroy(){this.clearRegions(),super.destroy()}}const{SvelteComponent:Gi,check_outros:Zi,create_component:ot,destroy_component:lt,detach:Yi,empty:Ji,flush:Ki,group_outros:Qi,init:en,insert:tn,mount_component:at,safe_not_equal:nn,transition_in:De,transition_out:Re}=window.__gradio__svelte__internal;function sn(s){let e,t;return e=new Si({}),{c(){ot(e.$$.fragment)},m(i,n){at(e,i,n),t=!0},i(i){t||(De(e.$$.fragment,i),t=!0)},o(i){Re(e.$$.fragment,i),t=!1},d(i){lt(e,i)}}}function rn(s){let e,t;return e=new gi({}),{c(){ot(e.$$.fragment)},m(i,n){at(e,i,n),t=!0},i(i){t||(De(e.$$.fragment,i),t=!0)},o(i){Re(e.$$.fragment,i),t=!1},d(i){lt(e,i)}}}function on(s){let e,t;return e=new Ai({}),{c(){ot(e.$$.fragment)},m(i,n){at(e,i,n),t=!0},i(i){t||(De(e.$$.fragment,i),t=!0)},o(i){Re(e.$$.fragment,i),t=!1},d(i){lt(e,i)}}}function ln(s){let e,t,i,n;const r=[on,rn,sn],o=[];function u(a,d){return a[0]==0?0:a[0]<.5?1:a[0]>=.5?2:-1}return~(e=u(s))&&(t=o[e]=r[e](s)),{c(){t&&t.c(),i=Ji()},m(a,d){~e&&o[e].m(a,d),tn(a,i,d),n=!0},p(a,[d]){let l=e;e=u(a),e!==l&&(t&&(Qi(),Re(o[l],1,1,()=>{o[l]=null}),Zi()),~e?(t=o[e],t||(t=o[e]=r[e](a),t.c()),De(t,1),t.m(i.parentNode,i)):t=null)},i(a){n||(De(t),n=!0)},o(a){Re(t),n=!1},d(a){a&&Yi(i),~e&&o[e].d(a)}}}function an(s,e,t){let{currentVolume:i}=e;return s.$$set=n=>{"currentVolume"in n&&t(0,i=n.currentVolume)},[i]}class un extends Gi{constructor(e){super(),en(this,e,an,ln,nn,{currentVolume:0})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),Ki()}}const{SvelteComponent:dn,attr:ve,binding_callbacks:cn,detach:hn,element:fn,flush:Je,init:mn,insert:_n,listen:vt,noop:bt,run_all:pn,safe_not_equal:gn}=window.__gradio__svelte__internal,{onMount:vn}=window.__gradio__svelte__internal;function bn(s){let e,t,i;return{c(){e=fn("input"),ve(e,"id","volume"),ve(e,"class","volume-slider svelte-wuo8j5"),ve(e,"type","range"),ve(e,"min","0"),ve(e,"max","1"),ve(e,"step","0.01"),e.value=s[0]},m(n,r){_n(n,e,r),s[4](e),t||(i=[vt(e,"focusout",s[5]),vt(e,"input",s[6])],t=!0)},p(n,[r]){r&1&&(e.value=n[0])},i:bt,o:bt,d(n){n&&hn(e),s[4](null),t=!1,pn(i)}}}function wn(s,e,t){let{currentVolume:i=1}=e,{show_volume_slider:n=!1}=e,{waveform:r}=e,o;vn(()=>{u()});const u=()=>{let c=o;c&&(c.style.background=`linear-gradient(to right, var(--color-accent) ${i*100}%, var(--neutral-400) ${i*100}%)`)};function a(c){cn[c?"unshift":"push"](()=>{o=c,t(3,o)})}const d=()=>t(1,n=!1),l=c=>{c.target instanceof HTMLInputElement&&(t(0,i=parseFloat(c.target.value)),r?.setVolume(i))};return s.$$set=c=>{"currentVolume"in c&&t(0,i=c.currentVolume),"show_volume_slider"in c&&t(1,n=c.show_volume_slider),"waveform"in c&&t(2,r=c.waveform)},s.$$.update=()=>{s.$$.dirty&1&&u()},[i,n,r,o,a,d,l]}class yn extends dn{constructor(e){super(),mn(this,e,wn,bn,gn,{currentVolume:0,show_volume_slider:1,waveform:2})}get currentVolume(){return this.$$.ctx[0]}set currentVolume(e){this.$$set({currentVolume:e}),Je()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),Je()}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),Je()}}const{SvelteComponent:kn,add_flush_callback:wt,append:O,attr:D,bind:yt,binding_callbacks:kt,check_outros:Le,create_component:ne,destroy_component:se,detach:re,element:B,empty:Cn,flush:N,group_outros:$e,init:En,insert:oe,listen:Q,mount_component:le,noop:Me,run_all:Ht,safe_not_equal:Sn,set_data:Ln,set_style:Ct,space:ie,text:Et,toggle_class:St,transition_in:P,transition_out:z}=window.__gradio__svelte__internal;function Lt(s){let e,t,i,n;function r(a){s[27](a)}function o(a){s[28](a)}let u={waveform:s[2]};return s[12]!==void 0&&(u.currentVolume=s[12]),s[1]!==void 0&&(u.show_volume_slider=s[1]),e=new yn({props:u}),kt.push(()=>yt(e,"currentVolume",r)),kt.push(()=>yt(e,"show_volume_slider",o)),{c(){ne(e.$$.fragment)},m(a,d){le(e,a,d),n=!0},p(a,d){const l={};d[0]&4&&(l.waveform=a[2]),!t&&d[0]&4096&&(t=!0,l.currentVolume=a[12],wt(()=>t=!1)),!i&&d[0]&2&&(i=!0,l.show_volume_slider=a[1],wt(()=>i=!1)),e.$set(l)},i(a){n||(P(e.$$.fragment,a),n=!0)},o(a){z(e.$$.fragment,a),n=!1},d(a){se(e,a)}}}function $n(s){let e,t;return e=new It({}),{c(){ne(e.$$.fragment)},m(i,n){le(e,i,n),t=!0},i(i){t||(P(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Mn(s){let e,t;return e=new Ut({}),{c(){ne(e.$$.fragment)},m(i,n){le(e,i,n),t=!0},i(i){t||(P(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function $t(s){let e,t,i,n,r,o=s[6]&&s[0]===""&&Mt(s);const u=[Rn,Dn],a=[];function d(l,c){return l[0]===""?0:1}return t=d(s),i=a[t]=u[t](s),{c(){o&&o.c(),e=ie(),i.c(),n=Cn()},m(l,c){o&&o.m(l,c),oe(l,e,c),a[t].m(l,c),oe(l,n,c),r=!0},p(l,c){l[6]&&l[0]===""?o?(o.p(l,c),c[0]&65&&P(o,1)):(o=Mt(l),o.c(),P(o,1),o.m(e.parentNode,e)):o&&($e(),z(o,1,1,()=>{o=null}),Le());let _=t;t=d(l),t===_?a[t].p(l,c):($e(),z(a[_],1,1,()=>{a[_]=null}),Le(),i=a[t],i?i.p(l,c):(i=a[t]=u[t](l),i.c()),P(i,1),i.m(n.parentNode,n))},i(l){r||(P(o),P(i),r=!0)},o(l){z(o),z(i),r=!1},d(l){l&&(re(e),re(n)),o&&o.d(l),a[t].d(l)}}}function Mt(s){let e,t,i,n,r;return t=new Ft({}),{c(){e=B("button"),ne(t.$$.fragment),D(e,"class","action icon svelte-ije4bl"),D(e,"aria-label","Reset audio")},m(o,u){oe(o,e,u),le(t,e,null),i=!0,n||(r=Q(e,"click",s[33]),n=!0)},p:Me,i(o){i||(P(t.$$.fragment,o),i=!0)},o(o){z(t.$$.fragment,o),i=!1},d(o){o&&re(e),se(t),n=!1,r()}}}function Dn(s){let e,t,i,n,r;return{c(){e=B("button"),e.textContent="Trim",t=ie(),i=B("button"),i.textContent="Cancel",D(e,"class","text-button svelte-ije4bl"),D(i,"class","text-button svelte-ije4bl")},m(o,u){oe(o,e,u),oe(o,t,u),oe(o,i,u),n||(r=[Q(e,"click",s[14]),Q(i,"click",s[16])],n=!0)},p:Me,i:Me,o:Me,d(o){o&&(re(e),re(t),re(i)),n=!1,Ht(r)}}}function Rn(s){let e,t,i,n,r;return t=new xt({}),{c(){e=B("button"),ne(t.$$.fragment),D(e,"class","action icon svelte-ije4bl"),D(e,"aria-label","Trim audio to selection")},m(o,u){oe(o,e,u),le(t,e,null),i=!0,n||(r=Q(e,"click",s[16]),n=!0)},p:Me,i(o){i||(P(t.$$.fragment,o),i=!0)},o(o){z(t.$$.fragment,o),i=!1},d(o){o&&re(e),se(t),n=!1,r()}}}function Pn(s){let e,t,i,n,r,o,u,a,d,l,c,_,m,g,C,E,b,w,L,k,h,$,p,S,M,J,I,H,ae,ue;n=new un({props:{currentVolume:s[12]}});let R=s[1]&&Lt(s);C=new ti({});const fe=[Mn,$n],G=[];function me(y,T){return y[5]?0:1}L=me(s),k=G[L]=fe[L](s),S=new ui({});let f=s[10]&&s[7]&&$t(s);return{c(){e=B("div"),t=B("div"),i=B("button"),ne(n.$$.fragment),r=ie(),R&&R.c(),o=ie(),u=B("button"),a=B("span"),d=Et(s[11]),l=Et("x"),_=ie(),m=B("div"),g=B("button"),ne(C.$$.fragment),b=ie(),w=B("button"),k.c(),$=ie(),p=B("button"),ne(S.$$.fragment),J=ie(),I=B("div"),f&&f.c(),D(i,"class","action icon volume svelte-ije4bl"),D(i,"aria-label","Adjust volume"),Ct(i,"color",s[1]?"var(--color-accent)":"var(--neutral-400)"),D(u,"class","playback icon svelte-ije4bl"),D(u,"aria-label",c=`Adjust playback speed to ${s[13][(s[13].indexOf(s[11])+1)%s[13].length]}x`),St(u,"hidden",s[1]),D(t,"class","control-wrapper svelte-ije4bl"),D(g,"class","rewind icon svelte-ije4bl"),D(g,"aria-label",E=`Skip backwards by ${we(s[3],s[9].skip_length)} seconds`),D(w,"class","play-pause-button icon svelte-ije4bl"),D(w,"aria-label",h=s[5]?s[4]("audio.pause"):s[4]("audio.play")),D(p,"class","skip icon svelte-ije4bl"),D(p,"aria-label",M="Skip forward by "+we(s[3],s[9].skip_length)+" seconds"),D(m,"class","play-pause-wrapper svelte-ije4bl"),D(I,"class","settings-wrapper svelte-ije4bl"),D(e,"class","controls svelte-ije4bl"),D(e,"data-testid","waveform-controls")},m(y,T){oe(y,e,T),O(e,t),O(t,i),le(n,i,null),O(t,r),R&&R.m(t,null),O(t,o),O(t,u),O(u,a),O(a,d),O(a,l),O(e,_),O(e,m),O(m,g),le(C,g,null),O(m,b),O(m,w),G[L].m(w,null),O(m,$),O(m,p),le(S,p,null),O(e,J),O(e,I),f&&f.m(I,null),H=!0,ae||(ue=[Q(i,"click",s[26]),Q(u,"click",s[29]),Q(g,"click",s[30]),Q(w,"click",s[31]),Q(p,"click",s[32])],ae=!0)},p(y,T){const _e={};T[0]&4096&&(_e.currentVolume=y[12]),n.$set(_e),T[0]&2&&Ct(i,"color",y[1]?"var(--color-accent)":"var(--neutral-400)"),y[1]?R?(R.p(y,T),T[0]&2&&P(R,1)):(R=Lt(y),R.c(),P(R,1),R.m(t,o)):R&&($e(),z(R,1,1,()=>{R=null}),Le()),(!H||T[0]&2048)&&Ln(d,y[11]),(!H||T[0]&2048&&c!==(c=`Adjust playback speed to ${y[13][(y[13].indexOf(y[11])+1)%y[13].length]}x`))&&D(u,"aria-label",c),(!H||T[0]&2)&&St(u,"hidden",y[1]),(!H||T[0]&520&&E!==(E=`Skip backwards by ${we(y[3],y[9].skip_length)} seconds`))&&D(g,"aria-label",E);let v=L;L=me(y),L!==v&&($e(),z(G[v],1,1,()=>{G[v]=null}),Le(),k=G[L],k||(k=G[L]=fe[L](y),k.c()),P(k,1),k.m(w,null)),(!H||T[0]&48&&h!==(h=y[5]?y[4]("audio.pause"):y[4]("audio.play")))&&D(w,"aria-label",h),(!H||T[0]&520&&M!==(M="Skip forward by "+we(y[3],y[9].skip_length)+" seconds"))&&D(p,"aria-label",M),y[10]&&y[7]?f?(f.p(y,T),T[0]&1152&&P(f,1)):(f=$t(y),f.c(),P(f,1),f.m(I,null)):f&&($e(),z(f,1,1,()=>{f=null}),Le())},i(y){H||(P(n.$$.fragment,y),P(R),P(C.$$.fragment,y),P(k),P(S.$$.fragment,y),P(f),H=!0)},o(y){z(n.$$.fragment,y),z(R),z(C.$$.fragment,y),z(k),z(S.$$.fragment,y),z(f),H=!1},d(y){y&&re(e),se(n),R&&R.d(),se(C),G[L].d(),se(S),f&&f.d(),ae=!1,Ht(ue)}}}function Tn(s,e,t){let{waveform:i}=e,{audio_duration:n}=e,{i18n:r}=e,{playing:o}=e,{show_redo:u=!1}=e,{interactive:a=!1}=e,{handle_trim_audio:d}=e,{mode:l=""}=e,{container:c}=e,{handle_reset_value:_}=e,{waveform_options:m={}}=e,{trim_region_settings:g={}}=e,{show_volume_slider:C=!1}=e,{editable:E=!0}=e,{trimDuration:b=0}=e,w=[.5,1,1.5,2],L=w[1],k=null,h=null,$,p,S="",M=1;const J=()=>{k&&(t(22,h=k?.addRegion({start:n/4,end:n/2,...g})),t(17,b=h.end-h.start))},I=()=>{if(i&&k&&h){const v=h.start,pe=h.end;d(v,pe),t(0,l=""),t(22,h=null)}},H=()=>{k?.getRegions().forEach(v=>{v.remove()}),k?.clearRegions()},ae=()=>{H(),l==="edit"?t(0,l=""):(t(0,l="edit"),J())},ue=(v,pe)=>{let ke,Ce;h&&(v==="left"?pe==="ArrowLeft"?(ke=h.start-.05,Ce=h.end):(ke=h.start+.05,Ce=h.end):pe==="ArrowLeft"?(ke=h.start,Ce=h.end-.05):(ke=h.start,Ce=h.end+.05),h.setOptions({start:ke,end:Ce}),t(17,b=h.end-h.start))},R=()=>t(1,C=!C);function fe(v){M=v,t(12,M)}function G(v){C=v,t(1,C)}const me=()=>{t(11,L=w[(w.indexOf(L)+1)%w.length]),i?.setPlaybackRate(L)},f=()=>i?.skip(we(n,m.skip_length)*-1),y=()=>i?.playPause(),T=()=>i?.skip(we(n,m.skip_length)),_e=()=>{_(),H(),t(0,l="")};return s.$$set=v=>{"waveform"in v&&t(2,i=v.waveform),"audio_duration"in v&&t(3,n=v.audio_duration),"i18n"in v&&t(4,r=v.i18n),"playing"in v&&t(5,o=v.playing),"show_redo"in v&&t(6,u=v.show_redo),"interactive"in v&&t(7,a=v.interactive),"handle_trim_audio"in v&&t(18,d=v.handle_trim_audio),"mode"in v&&t(0,l=v.mode),"container"in v&&t(19,c=v.container),"handle_reset_value"in v&&t(8,_=v.handle_reset_value),"waveform_options"in v&&t(9,m=v.waveform_options),"trim_region_settings"in v&&t(20,g=v.trim_region_settings),"show_volume_slider"in v&&t(1,C=v.show_volume_slider),"editable"in v&&t(10,E=v.editable),"trimDuration"in v&&t(17,b=v.trimDuration)},s.$$.update=()=>{if(s.$$.dirty[0]&524292&&t(21,k=c&&i?i.registerPlugin(rt.create()):null),s.$$.dirty[0]&2097152&&k?.on("region-out",v=>{v.play()}),s.$$.dirty[0]&2097152&&k?.on("region-updated",v=>{t(17,b=v.end-v.start)}),s.$$.dirty[0]&2097152&&k?.on("region-clicked",(v,pe)=>{pe.stopPropagation(),t(22,h=v),v.play()}),s.$$.dirty[0]&31981568&&h){const v=c.children[0].shadowRoot;t(24,p=v.querySelector('[data-resize="right"]')),t(23,$=v.querySelector('[data-resize="left"]')),$&&p&&($.setAttribute("role","button"),p.setAttribute("role","button"),$?.setAttribute("aria-label","Drag to adjust start time"),p?.setAttribute("aria-label","Drag to adjust end time"),$?.setAttribute("tabindex","0"),p?.setAttribute("tabindex","0"),$.addEventListener("focus",()=>{k&&t(25,S="left")}),p.addEventListener("focus",()=>{k&&t(25,S="right")}))}s.$$.dirty[0]&35651584&&k&&window.addEventListener("keydown",v=>{v.key==="ArrowLeft"?ue(S,"ArrowLeft"):v.key==="ArrowRight"&&ue(S,"ArrowRight")})},[l,C,i,n,r,o,u,a,_,m,E,L,M,w,I,H,ae,b,d,c,g,k,h,$,p,S,R,fe,G,me,f,y,T,_e]}class An extends kn{constructor(e){super(),En(this,e,Tn,Pn,Sn,{waveform:2,audio_duration:3,i18n:4,playing:5,show_redo:6,interactive:7,handle_trim_audio:18,mode:0,container:19,handle_reset_value:8,waveform_options:9,trim_region_settings:20,show_volume_slider:1,editable:10,trimDuration:17},null,[-1,-1])}get waveform(){return this.$$.ctx[2]}set waveform(e){this.$$set({waveform:e}),N()}get audio_duration(){return this.$$.ctx[3]}set audio_duration(e){this.$$set({audio_duration:e}),N()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),N()}get playing(){return this.$$.ctx[5]}set playing(e){this.$$set({playing:e}),N()}get show_redo(){return this.$$.ctx[6]}set show_redo(e){this.$$set({show_redo:e}),N()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),N()}get handle_trim_audio(){return this.$$.ctx[18]}set handle_trim_audio(e){this.$$set({handle_trim_audio:e}),N()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),N()}get container(){return this.$$.ctx[19]}set container(e){this.$$set({container:e}),N()}get handle_reset_value(){return this.$$.ctx[8]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),N()}get waveform_options(){return this.$$.ctx[9]}set waveform_options(e){this.$$set({waveform_options:e}),N()}get trim_region_settings(){return this.$$.ctx[20]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),N()}get show_volume_slider(){return this.$$.ctx[1]}set show_volume_slider(e){this.$$set({show_volume_slider:e}),N()}get editable(){return this.$$.ctx[10]}set editable(e){this.$$set({editable:e}),N()}get trimDuration(){return this.$$.ctx[17]}set trimDuration(e){this.$$set({trimDuration:e}),N()}}const{SvelteComponent:Wn,add_flush_callback:Ke,append:Z,attr:V,bind:Qe,binding_callbacks:ye,bubble:zn,check_outros:On,create_component:ut,destroy_component:dt,detach:Be,element:K,empty:Hn,flush:X,group_outros:Vn,init:Nn,insert:Ie,listen:jn,mount_component:ct,noop:Dt,safe_not_equal:qn,set_data:Bn,set_style:Rt,space:ze,src_url_equal:Pt,text:In,transition_in:Pe,transition_out:Te}=window.__gradio__svelte__internal,{onMount:Un}=window.__gradio__svelte__internal,{createEventDispatcher:xn}=window.__gradio__svelte__internal;function Fn(s){let e,t,i,n,r,o,u,a,d,l,c,_,m,g,C,E,b,w=s[0]==="edit"&&s[17]>0&&Tt(s);function L(p){s[26](p)}function k(p){s[27](p)}function h(p){s[28](p)}let $={container:s[11],waveform:s[12],playing:s[15],audio_duration:s[16],i18n:s[3],interactive:s[4],handle_trim_audio:s[19],show_redo:s[4],handle_reset_value:s[10],waveform_options:s[8],trim_region_settings:s[6],editable:s[5]};return s[0]!==void 0&&($.mode=s[0]),s[17]!==void 0&&($.trimDuration=s[17]),s[18]!==void 0&&($.show_volume_slider=s[18]),_=new An({props:$}),ye.push(()=>Qe(_,"mode",L)),ye.push(()=>Qe(_,"trimDuration",k)),ye.push(()=>Qe(_,"show_volume_slider",h)),{c(){e=K("div"),t=K("div"),i=K("div"),n=ze(),r=K("div"),o=K("time"),o.textContent="0:00",u=ze(),a=K("div"),w&&w.c(),d=ze(),l=K("time"),l.textContent="0:00",c=ze(),ut(_.$$.fragment),V(i,"id","waveform"),V(i,"class","svelte-1ark3ru"),Rt(i,"height",s[11]?null:"58px"),V(t,"class","waveform-container svelte-1ark3ru"),V(o,"id","time"),V(o,"class","svelte-1ark3ru"),V(l,"id","duration"),V(l,"class","svelte-1ark3ru"),V(r,"class","timestamps svelte-1ark3ru"),V(e,"class","component-wrapper svelte-1ark3ru"),V(e,"data-testid",E=s[2]?"waveform-"+s[2]:"unlabelled-audio")},m(p,S){Ie(p,e,S),Z(e,t),Z(t,i),s[23](i),Z(e,n),Z(e,r),Z(r,o),s[24](o),Z(r,u),Z(r,a),w&&w.m(a,null),Z(a,d),Z(a,l),s[25](l),Z(e,c),ct(_,e,null),b=!0},p(p,S){S[0]&2048&&Rt(i,"height",p[11]?null:"58px"),p[0]==="edit"&&p[17]>0?w?w.p(p,S):(w=Tt(p),w.c(),w.m(a,d)):w&&(w.d(1),w=null);const M={};S[0]&2048&&(M.container=p[11]),S[0]&4096&&(M.waveform=p[12]),S[0]&32768&&(M.playing=p[15]),S[0]&65536&&(M.audio_duration=p[16]),S[0]&8&&(M.i18n=p[3]),S[0]&16&&(M.interactive=p[4]),S[0]&16&&(M.show_redo=p[4]),S[0]&1024&&(M.handle_reset_value=p[10]),S[0]&256&&(M.waveform_options=p[8]),S[0]&64&&(M.trim_region_settings=p[6]),S[0]&32&&(M.editable=p[5]),!m&&S[0]&1&&(m=!0,M.mode=p[0],Ke(()=>m=!1)),!g&&S[0]&131072&&(g=!0,M.trimDuration=p[17],Ke(()=>g=!1)),!C&&S[0]&262144&&(C=!0,M.show_volume_slider=p[18],Ke(()=>C=!1)),_.$set(M),(!b||S[0]&4&&E!==(E=p[2]?"waveform-"+p[2]:"unlabelled-audio"))&&V(e,"data-testid",E)},i(p){b||(Pe(_.$$.fragment,p),b=!0)},o(p){Te(_.$$.fragment,p),b=!1},d(p){p&&Be(e),s[23](null),s[24](null),w&&w.d(),s[25](null),dt(_)}}}function Xn(s){let e,t,i,n,r;return{c(){e=K("audio"),V(e,"class","standard-player svelte-1ark3ru"),Pt(e.src,t=s[1].url)||V(e,"src",t),e.controls=!0,e.autoplay=i=s[7].autoplay,e.loop=s[9]},m(o,u){Ie(o,e,u),n||(r=jn(e,"load",s[22]),n=!0)},p(o,u){u[0]&2&&!Pt(e.src,t=o[1].url)&&V(e,"src",t),u[0]&128&&i!==(i=o[7].autoplay)&&(e.autoplay=i),u[0]&512&&(e.loop=o[9])},i:Dt,o:Dt,d(o){o&&Be(e),n=!1,r()}}}function Gn(s){let e,t;return e=new zt({props:{size:"small",$$slots:{default:[Zn]},$$scope:{ctx:s}}}),{c(){ut(e.$$.fragment)},m(i,n){ct(e,i,n),t=!0},p(i,n){const r={};n[1]&2&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){t||(Pe(e.$$.fragment,i),t=!0)},o(i){Te(e.$$.fragment,i),t=!1},d(i){dt(e,i)}}}function Tt(s){let e,t=He(s[17])+"",i;return{c(){e=K("time"),i=In(t),V(e,"id","trim-duration"),V(e,"class","svelte-1ark3ru")},m(n,r){Ie(n,e,r),Z(e,i)},p(n,r){r[0]&131072&&t!==(t=He(n[17])+"")&&Bn(i,t)},d(n){n&&Be(e)}}}function Zn(s){let e,t;return e=new nt({}),{c(){ut(e.$$.fragment)},m(i,n){ct(e,i,n),t=!0},i(i){t||(Pe(e.$$.fragment,i),t=!0)},o(i){Te(e.$$.fragment,i),t=!1},d(i){dt(e,i)}}}function Yn(s){let e,t,i,n;const r=[Gn,Xn,Fn],o=[];function u(a,d){return a[1]===null?0:a[1].is_stream?1:2}return e=u(s),t=o[e]=r[e](s),{c(){t.c(),i=Hn()},m(a,d){o[e].m(a,d),Ie(a,i,d),n=!0},p(a,d){let l=e;e=u(a),e===l?o[e].p(a,d):(Vn(),Te(o[l],1,1,()=>{o[l]=null}),On(),t=o[e],t?t.p(a,d):(t=o[e]=r[e](a),t.c()),Pe(t,1),t.m(i.parentNode,i))},i(a){n||(Pe(t),n=!0)},o(a){Te(t),n=!1},d(a){a&&Be(i),o[e].d(a)}}}function Jn(s,e,t){let i,{value:n=null}=e,{label:r}=e,{i18n:o}=e,{dispatch_blob:u=()=>Promise.resolve()}=e,{interactive:a=!1}=e,{editable:d=!0}=e,{trim_region_settings:l={}}=e,{waveform_settings:c}=e,{waveform_options:_}=e,{mode:m=""}=e,{loop:g}=e,{handle_reset_value:C=()=>{}}=e,E,b,w=!1,L,k,h,$=0,p=!1;const S=xn(),M=()=>{t(12,b=st.create({container:E,...c})),ht(n?.url).then(f=>{if(f&&b)return b.load(f)})},J=async(f,y)=>{t(0,m="");const T=b?.getDecodedData();T&&await Fi(T,f,y,c.sampleRate).then(async _e=>{await u([_e],"change"),b?.destroy(),t(11,E.innerHTML="",E)}),S("edit")};async function I(f){await ht(f).then(y=>{if(!(!y||n?.is_stream))return b?.load(y)})}Un(()=>{window.addEventListener("keydown",f=>{!b||p||(f.key==="ArrowRight"&&m!=="edit"?pt(b,.1):f.key==="ArrowLeft"&&m!=="edit"&&pt(b,-.1))})});function H(f){zn.call(this,s,f)}function ae(f){ye[f?"unshift":"push"](()=>{E=f,t(11,E),t(12,b)})}function ue(f){ye[f?"unshift":"push"](()=>{L=f,t(13,L),t(12,b)})}function R(f){ye[f?"unshift":"push"](()=>{k=f,t(14,k),t(12,b)})}function fe(f){m=f,t(0,m)}function G(f){$=f,t(17,$)}function me(f){p=f,t(18,p)}return s.$$set=f=>{"value"in f&&t(1,n=f.value),"label"in f&&t(2,r=f.label),"i18n"in f&&t(3,o=f.i18n),"dispatch_blob"in f&&t(20,u=f.dispatch_blob),"interactive"in f&&t(4,a=f.interactive),"editable"in f&&t(5,d=f.editable),"trim_region_settings"in f&&t(6,l=f.trim_region_settings),"waveform_settings"in f&&t(7,c=f.waveform_settings),"waveform_options"in f&&t(8,_=f.waveform_options),"mode"in f&&t(0,m=f.mode),"loop"in f&&t(9,g=f.loop),"handle_reset_value"in f&&t(10,C=f.handle_reset_value)},s.$$.update=()=>{s.$$.dirty[0]&2&&t(21,i=n?.url),s.$$.dirty[0]&6144&&E!==void 0&&(b!==void 0&&b.destroy(),t(11,E.innerHTML="",E),M(),t(15,w=!1)),s.$$.dirty[0]&20480&&b?.on("decode",f=>{t(16,h=f),k&&t(14,k.textContent=He(f),k)}),s.$$.dirty[0]&12288&&b?.on("timeupdate",f=>L&&t(13,L.textContent=He(f),L)),s.$$.dirty[0]&4224&&b?.on("ready",()=>{c.autoplay?b?.play():b?.stop()}),s.$$.dirty[0]&4608&&b?.on("finish",()=>{g?b?.play():(t(15,w=!1),S("stop"))}),s.$$.dirty[0]&4096&&b?.on("pause",()=>{t(15,w=!1),S("pause")}),s.$$.dirty[0]&4096&&b?.on("play",()=>{t(15,w=!0),S("play")}),s.$$.dirty[0]&4096&&b?.on("load",()=>{S("load")}),s.$$.dirty[0]&2097152&&i&&I(i)},[m,n,r,o,a,d,l,c,_,g,C,E,b,L,k,w,h,$,p,J,u,i,H,ae,ue,R,fe,G,me]}class Kn extends Wn{constructor(e){super(),Nn(this,e,Jn,Yn,qn,{value:1,label:2,i18n:3,dispatch_blob:20,interactive:4,editable:5,trim_region_settings:6,waveform_settings:7,waveform_options:8,mode:0,loop:9,handle_reset_value:10},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),X()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),X()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),X()}get dispatch_blob(){return this.$$.ctx[20]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),X()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),X()}get editable(){return this.$$.ctx[5]}set editable(e){this.$$set({editable:e}),X()}get trim_region_settings(){return this.$$.ctx[6]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),X()}get waveform_settings(){return this.$$.ctx[7]}set waveform_settings(e){this.$$set({waveform_settings:e}),X()}get waveform_options(){return this.$$.ctx[8]}set waveform_options(e){this.$$set({waveform_options:e}),X()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),X()}get loop(){return this.$$.ctx[9]}set loop(e){this.$$set({loop:e}),X()}get handle_reset_value(){return this.$$.ctx[10]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),X()}}const Qn=Kn,{SvelteComponent:es,append:ts,attr:is,bubble:be,check_outros:et,create_component:de,destroy_component:ce,detach:Ve,element:ns,empty:ss,flush:Y,group_outros:tt,init:rs,insert:Ne,mount_component:he,safe_not_equal:os,space:it,transition_in:j,transition_out:U}=window.__gradio__svelte__internal,{createEventDispatcher:ls}=window.__gradio__svelte__internal;function as(s){let e,t;return e=new zt({props:{size:"small",$$slots:{default:[ds]},$$scope:{ctx:s}}}),{c(){de(e.$$.fragment)},m(i,n){he(e,i,n),t=!0},p(i,n){const r={};n&262144&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){t||(j(e.$$.fragment,i),t=!0)},o(i){U(e.$$.fragment,i),t=!1},d(i){ce(e,i)}}}function us(s){let e,t,i,n,r,o=s[3]&&At(s),u=s[4]&&Wt(s);return n=new Qn({props:{value:s[0],label:s[1],i18n:s[5],waveform_settings:s[6],waveform_options:s[7],editable:s[8],loop:s[9]}}),n.$on("pause",s[13]),n.$on("play",s[14]),n.$on("stop",s[15]),n.$on("load",s[16]),{c(){e=ns("div"),o&&o.c(),t=it(),u&&u.c(),i=it(),de(n.$$.fragment),is(e,"class","icon-buttons svelte-rvdo70")},m(a,d){Ne(a,e,d),o&&o.m(e,null),ts(e,t),u&&u.m(e,null),Ne(a,i,d),he(n,a,d),r=!0},p(a,d){a[3]?o?(o.p(a,d),d&8&&j(o,1)):(o=At(a),o.c(),j(o,1),o.m(e,t)):o&&(tt(),U(o,1,1,()=>{o=null}),et()),a[4]?u?(u.p(a,d),d&16&&j(u,1)):(u=Wt(a),u.c(),j(u,1),u.m(e,null)):u&&(tt(),U(u,1,1,()=>{u=null}),et());const l={};d&1&&(l.value=a[0]),d&2&&(l.label=a[1]),d&32&&(l.i18n=a[5]),d&64&&(l.waveform_settings=a[6]),d&128&&(l.waveform_options=a[7]),d&256&&(l.editable=a[8]),d&512&&(l.loop=a[9]),n.$set(l)},i(a){r||(j(o),j(u),j(n.$$.fragment,a),r=!0)},o(a){U(o),U(u),U(n.$$.fragment,a),r=!1},d(a){a&&(Ve(e),Ve(i)),o&&o.d(),u&&u.d(),ce(n,a)}}}function ds(s){let e,t;return e=new nt({}),{c(){de(e.$$.fragment)},m(i,n){he(e,i,n),t=!0},i(i){t||(j(e.$$.fragment,i),t=!0)},o(i){U(e.$$.fragment,i),t=!1},d(i){ce(e,i)}}}function At(s){let e,t;return e=new Xt({props:{href:s[0].url,download:s[0].orig_name||s[0].path,$$slots:{default:[cs]},$$scope:{ctx:s}}}),{c(){de(e.$$.fragment)},m(i,n){he(e,i,n),t=!0},p(i,n){const r={};n&1&&(r.href=i[0].url),n&1&&(r.download=i[0].orig_name||i[0].path),n&262176&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){t||(j(e.$$.fragment,i),t=!0)},o(i){U(e.$$.fragment,i),t=!1},d(i){ce(e,i)}}}function cs(s){let e,t;return e=new Vt({props:{Icon:Bt,label:s[5]("common.download")}}),{c(){de(e.$$.fragment)},m(i,n){he(e,i,n),t=!0},p(i,n){const r={};n&32&&(r.label=i[5]("common.download")),e.$set(r)},i(i){t||(j(e.$$.fragment,i),t=!0)},o(i){U(e.$$.fragment,i),t=!1},d(i){ce(e,i)}}}function Wt(s){let e,t;return e=new qt({props:{i18n:s[5],formatter:s[10],value:s[0]}}),e.$on("error",s[11]),e.$on("share",s[12]),{c(){de(e.$$.fragment)},m(i,n){he(e,i,n),t=!0},p(i,n){const r={};n&32&&(r.i18n=i[5]),n&1&&(r.value=i[0]),e.$set(r)},i(i){t||(j(e.$$.fragment,i),t=!0)},o(i){U(e.$$.fragment,i),t=!1},d(i){ce(e,i)}}}function hs(s){let e,t,i,n,r,o;e=new jt({props:{show_label:s[2],Icon:nt,float:!1,label:s[1]||s[5]("audio.audio")}});const u=[us,as],a=[];function d(l,c){return l[0]!==null?0:1}return i=d(s),n=a[i]=u[i](s),{c(){de(e.$$.fragment),t=it(),n.c(),r=ss()},m(l,c){he(e,l,c),Ne(l,t,c),a[i].m(l,c),Ne(l,r,c),o=!0},p(l,[c]){const _={};c&4&&(_.show_label=l[2]),c&34&&(_.label=l[1]||l[5]("audio.audio")),e.$set(_);let m=i;i=d(l),i===m?a[i].p(l,c):(tt(),U(a[m],1,1,()=>{a[m]=null}),et(),n=a[i],n?n.p(l,c):(n=a[i]=u[i](l),n.c()),j(n,1),n.m(r.parentNode,r))},i(l){o||(j(e.$$.fragment,l),j(n),o=!0)},o(l){U(e.$$.fragment,l),U(n),o=!1},d(l){l&&(Ve(t),Ve(r)),ce(e,l),a[i].d(l)}}}function fs(s,e,t){let{value:i=null}=e,{label:n}=e,{show_label:r=!0}=e,{show_download_button:o=!0}=e,{show_share_button:u=!1}=e,{i18n:a}=e,{waveform_settings:d}=e,{waveform_options:l}=e,{editable:c=!0}=e,{loop:_}=e;const m=ls(),g=async h=>h?`<audio controls src="${await Nt(h.url)}"></audio>`:"";function C(h){be.call(this,s,h)}function E(h){be.call(this,s,h)}function b(h){be.call(this,s,h)}function w(h){be.call(this,s,h)}function L(h){be.call(this,s,h)}function k(h){be.call(this,s,h)}return s.$$set=h=>{"value"in h&&t(0,i=h.value),"label"in h&&t(1,n=h.label),"show_label"in h&&t(2,r=h.show_label),"show_download_button"in h&&t(3,o=h.show_download_button),"show_share_button"in h&&t(4,u=h.show_share_button),"i18n"in h&&t(5,a=h.i18n),"waveform_settings"in h&&t(6,d=h.waveform_settings),"waveform_options"in h&&t(7,l=h.waveform_options),"editable"in h&&t(8,c=h.editable),"loop"in h&&t(9,_=h.loop)},s.$$.update=()=>{s.$$.dirty&1&&i&&m("change",i)},[i,n,r,o,u,a,d,l,c,_,g,C,E,b,w,L,k]}class ms extends es{constructor(e){super(),rs(this,e,fs,hs,os,{value:0,label:1,show_label:2,show_download_button:3,show_share_button:4,i18n:5,waveform_settings:6,waveform_options:7,editable:8,loop:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),Y()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),Y()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),Y()}get show_download_button(){return this.$$.ctx[3]}set show_download_button(e){this.$$set({show_download_button:e}),Y()}get show_share_button(){return this.$$.ctx[4]}set show_share_button(e){this.$$set({show_share_button:e}),Y()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),Y()}get waveform_settings(){return this.$$.ctx[6]}set waveform_settings(e){this.$$set({waveform_settings:e}),Y()}get waveform_options(){return this.$$.ctx[7]}set waveform_options(e){this.$$set({waveform_options:e}),Y()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),Y()}get loop(){return this.$$.ctx[9]}set loop(e){this.$$set({loop:e}),Y()}}const Ls=Object.freeze(Object.defineProperty({__proto__:null,default:ms},Symbol.toStringTag,{value:"Module"}));export{Qn as A,ms as S,An as W,st as a,Ls as b,Fi as p,pt as s};
//# sourceMappingURL=StaticAudio-Dw97rWIw.js.map
