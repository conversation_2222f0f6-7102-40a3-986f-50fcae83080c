const{SvelteComponent:i,append:v,attr:e,detach:c,init:h,insert:p,noop:o,safe_not_equal:_,svg_element:a}=window.__gradio__svelte__internal;function d(r){let t,n;return{c(){t=a("svg"),n=a("path"),e(n,"fill","currentColor"),e(n,"d","M26 24v4H6v-4H4v4a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2v-4zm0-10l-1.41-1.41L17 20.17V2h-2v18.17l-7.59-7.58L6 14l10 10l10-10z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 32 32")},m(l,s){p(l,t,s),v(t,n)},p:o,i:o,o,d(l){l&&c(t)}}}class w extends i{constructor(t){super(),h(this,t,null,d,_,{})}}export{w as D};
//# sourceMappingURL=Download-DVtk-Jv3.js.map
