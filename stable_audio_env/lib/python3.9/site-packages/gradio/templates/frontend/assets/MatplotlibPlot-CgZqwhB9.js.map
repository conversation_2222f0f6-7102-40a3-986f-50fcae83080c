{"version": 3, "file": "MatplotlibPlot-CgZqwhB9.js", "sources": ["../../../../js/plot/shared/plot_types/MatplotlibPlot.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value;\n\n\t$: plot = value?.plot;\n</script>\n\n<div data-testid={\"matplotlib\"} class=\"matplotlib layout\">\n\t<img\n\t\tsrc={plot}\n\t\talt={`${value.chart} plot visualising provided data`}\n\t\ton:load\n\t/>\n</div>\n\n<style>\n\t.layout {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t}\n\t.matplotlib img {\n\t\tobject-fit: contain;\n\t}\n</style>\n"], "names": ["ctx", "attr", "img", "img_src_value", "img_alt_value", "insert", "target", "div", "anchor", "append", "dirty", "value", "$$props", "$$invalidate", "plot"], "mappings": "yPAQOA,EAAI,CAAA,CAAA,GAAAC,EAAAC,EAAA,MAAAC,CAAA,EACDF,EAAAC,EAAA,MAAAE,EAAA,GAAAJ,KAAM,KAAK,iCAAA,iDAHH,YAAY,yDAA9BK,EAMKC,EAAAC,EAAAC,CAAA,EALJC,EAICF,EAAAL,CAAA,yDAHKF,EAAI,CAAA,CAAA,gBACDU,EAAA,GAAAN,KAAAA,EAAA,GAAAJ,KAAM,KAAK,2GART,MAAAW,CAAK,EAAAC,gHAEbC,EAAA,EAAAC,EAAOH,GAAO,IAAI"}