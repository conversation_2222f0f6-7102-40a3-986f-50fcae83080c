const{SvelteComponent:$,append:v,attr:n,detach:f,init:y,insert:k,noop:u,safe_not_equal:C,svg_element:h}=window.__gradio__svelte__internal;function q(d){let e,o,r;return{c(){e=h("svg"),o=h("rect"),r=h("rect"),n(o,"x","6"),n(o,"y","4"),n(o,"width","4"),n(o,"height","16"),n(r,"x","14"),n(r,"y","4"),n(r,"width","4"),n(r,"height","16"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","currentColor"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(i,s){k(i,e,s),v(e,o),v(e,r)},p:u,i:u,o:u,d(i){i&&f(e)}}}class I extends ${constructor(e){super(),y(this,e,null,q,C,{})}}const{SvelteComponent:S,append:j,attr:l,detach:B,init:P,insert:T,noop:g,safe_not_equal:b,svg_element:x}=window.__gradio__svelte__internal;function z(d){let e,o;return{c(){e=x("svg"),o=x("polygon"),l(o,"points","5 3 19 12 5 21 5 3"),l(e,"xmlns","http://www.w3.org/2000/svg"),l(e,"width","100%"),l(e,"height","100%"),l(e,"viewBox","0 0 24 24"),l(e,"fill","currentColor"),l(e,"stroke","currentColor"),l(e,"stroke-width","1.5"),l(e,"stroke-linecap","round"),l(e,"stroke-linejoin","round")},m(r,i){T(r,e,i),j(e,o)},p:g,i:g,o:g,d(r){r&&B(e)}}}class J extends S{constructor(e){super(),P(this,e,null,z,b,{})}}const{SvelteComponent:A,append:p,attr:t,detach:D,init:E,insert:F,noop:w,safe_not_equal:G,svg_element:a}=window.__gradio__svelte__internal;function H(d){let e,o,r,i,s,c;return{c(){e=a("svg"),o=a("circle"),r=a("circle"),i=a("line"),s=a("line"),c=a("line"),t(o,"cx","6"),t(o,"cy","6"),t(o,"r","3"),t(r,"cx","6"),t(r,"cy","18"),t(r,"r","3"),t(i,"x1","20"),t(i,"y1","4"),t(i,"x2","8.12"),t(i,"y2","15.88"),t(s,"x1","14.47"),t(s,"y1","14.48"),t(s,"x2","20"),t(s,"y2","20"),t(c,"x1","8.12"),t(c,"y1","8.12"),t(c,"x2","12"),t(c,"y2","12"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-scissors")},m(_,m){F(_,e,m),p(e,o),p(e,r),p(e,i),p(e,s),p(e,c)},p:w,i:w,o:w,d(_){_&&D(e)}}}class K extends A{constructor(e){super(),E(this,e,null,H,G,{})}}export{I as P,K as T,J as a};
//# sourceMappingURL=Trim-UKwaW4UI.js.map
