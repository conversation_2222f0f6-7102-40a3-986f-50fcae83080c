{"version": 3, "file": "Index-BfLZDIPR.js", "sources": ["../../../../js/tabs/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { TABS } from \"./shared/Tabs.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport Tabs from \"./shared/Tabs.svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let visible = true;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let selected: number | string;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t}>;\n\n\t$: dispatch(\"prop_change\", { selected });\n</script>\n\n<Tabs\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tbind:selected\n\ton:change={() => gradio.dispatch(\"change\")}\n\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n>\n\t<slot />\n</Tabs>\n"], "names": ["createEventDispatcher", "dispatch", "visible", "$$props", "elem_id", "elem_classes", "selected", "gradio", "e"], "mappings": "ybAMU,CAAA,sBAAAA,CAAA,SAAqC,s3BAGxC,MAAAC,EAAWD,IAEN,GAAA,CAAA,QAAAE,EAAU,EAAI,EAAAC,EACd,CAAA,QAAAC,EAAU,EAAE,EAAAD,GACZ,aAAAE,EAAY,EAAA,EAAAF,GACZ,SAAAG,CAAyB,EAAAH,GACzB,OAAAI,CAGT,EAAAJ,uCAUeI,EAAO,SAAS,QAAQ,IAC7BC,GAAMD,EAAO,SAAS,SAAUC,EAAE,MAAM,+PATjDP,EAAS,eAAiB,SAAAK,CAAQ,CAAA"}