laion_clap-1.1.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
laion_clap-1.1.4.dist-info/LICENSE,sha256=ogEPNDSH0_dhiv_lT3ifVIdgIzHAqNA_SemnxUfPBJk,7048
laion_clap-1.1.4.dist-info/METADATA,sha256=vdLdKy09fllXp3NTIUc8SUhZlV8i3gadoBKGNXIBCoM,26249
laion_clap-1.1.4.dist-info/RECORD,,
laion_clap-1.1.4.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
laion_clap-1.1.4.dist-info/top_level.txt,sha256=RpiqFLRtvGulFnt8Okdm0PkYL_S6PRCyu2UecrCxhv4,17
laion_clap/__init__.py,sha256=F__wL6YYjYmEUnv3GhyXIyM_kjaqjXU1yrOZX2DQW9Y,130
laion_clap/__pycache__/__init__.cpython-39.pyc,,
laion_clap/__pycache__/hook.cpython-39.pyc,,
laion_clap/__pycache__/unit_test.cpython-39.pyc,,
laion_clap/clap_module/__init__.py,sha256=RR4iX-L8uoPX8Jj_wVYpKs1Ajq5yIsrTFA8mVrEj-TQ,568
laion_clap/clap_module/__pycache__/__init__.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/bert.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/factory.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/feature_fusion.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/htsat.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/linear_probe.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/loss.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/model.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/openai.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/pann_model.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/pretrained.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/timm_model.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/tokenizer.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/transform.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/utils.cpython-39.pyc,,
laion_clap/clap_module/__pycache__/version.cpython-39.pyc,,
laion_clap/clap_module/bert.py,sha256=RrwzvgCQvX8dxN0CqYVlWe1P729dK1FkAkCteDUe6ss,1215
laion_clap/clap_module/bpe_simple_vocab_16e6.txt.gz,sha256=kkaRrCiOVECSNhFWUq1KolD0ggPeUKnkcipuzUjWgEo,1356917
laion_clap/clap_module/factory.py,sha256=7L1Awm8PjL-vDiHTsH8dycUFRNb4WhtefDc4YrvboXI,10593
laion_clap/clap_module/feature_fusion.py,sha256=h4yY6SbgxzTkacfPlBnPC9mK-bDyJPZvBMq8Kzutap8,7182
laion_clap/clap_module/htsat.py,sha256=JbgXgZ_ZDSfdWiU5dQ6YTFWzlMw8AAsOuiD0s3I6dOs,45110
laion_clap/clap_module/linear_probe.py,sha256=6O-J72IrdsnlYNC6xsczcC_G2-Po6McQV1B6jSCBDN4,2204
laion_clap/clap_module/loss.py,sha256=wr6M3W8UjVUmvbwnNQ67z6IeJplhv1kvY3yZKECsIUw,14405
laion_clap/clap_module/model.py,sha256=GzY5zdyXfPMEucZONZ2s8t-qtJ-RxCCb2dKfYmgiLsQ,32754
laion_clap/clap_module/model_configs/HTSAT-base.json,sha256=6g8nW90LeABTuA0DUcAaFBpNtpuVSYpi5OwfFr91um8,497
laion_clap/clap_module/model_configs/HTSAT-large.json,sha256=LWeG4XtLrIqxqAPblEAKLB4o0UMYJz64vBOIPgCUOA4,498
laion_clap/clap_module/model_configs/HTSAT-tiny-win-1536.json,sha256=_h319xiQW94uSP6kqIWfQ96hyev0FHGSFOjP8HXfnsc,496
laion_clap/clap_module/model_configs/HTSAT-tiny.json,sha256=VbnF-l3Zu1UNMnL1XBiO9LCa0kMM0764VpFSaGHlYlY,496
laion_clap/clap_module/model_configs/PANN-10.json,sha256=gm8sExS-oLtvTJuMW6cCEdTv5yc2t-xEm7YWBbjGAlg,497
laion_clap/clap_module/model_configs/PANN-14-fmax-18k.json,sha256=folnzXdQ2uul5-7pyYh3IC-ZwzpPaeWkhR7pD39BT1w,497
laion_clap/clap_module/model_configs/PANN-14-fmax-8k-20s.json,sha256=OkWAOwmKVbApmcG1BMoqbeWI4PUlzzgXY2Ce_PRJ36M,496
laion_clap/clap_module/model_configs/PANN-14-tiny-transformer.json,sha256=BH4BXnvil_1MKCJTiTi-jDIkfgyGNFK6H9vmRyN874E,496
laion_clap/clap_module/model_configs/PANN-14-win-1536.json,sha256=OajOSzEB3ZvdbFcFXlyYco9xjM8gOejsTlMbwBew_7w,497
laion_clap/clap_module/model_configs/PANN-14.json,sha256=_R82I0Uma5LGum_ApEURNpEn-mGlbAJ8QnWXyuK78KA,497
laion_clap/clap_module/model_configs/PANN-6.json,sha256=yjwq8eMJF-QlA9e4E-YMKdZbVJH68dzkbrmHUargt1Q,495
laion_clap/clap_module/model_configs/RN101-quickgelu.json,sha256=JN1DXuxocddnW9awIJjnHhBalXqmo6WRU7AxgRA3VrU,388
laion_clap/clap_module/model_configs/RN101.json,sha256=BGaq5ywc0lmoZV4lrzFw1UrLTLrJUwTLmDLWjgcWU4s,364
laion_clap/clap_module/model_configs/RN50-quickgelu.json,sha256=B56cTz14CKdn3ggGGrQX-Y2eiqrs7GUQlJytDAw4mkE,389
laion_clap/clap_module/model_configs/RN50.json,sha256=pbmEWkS1oFL7FpcFW0G0khcaqEho8UCOX-1-YHfQRtQ,364
laion_clap/clap_module/model_configs/RN50x16.json,sha256=lxBkeH_cAma7Udkk229_UxF2MRhzMxW1TyqeUr22TVA,365
laion_clap/clap_module/model_configs/RN50x4.json,sha256=rqObTslSzP4bf5JCqcDTbhq-uQ9cb9Syz6xuQCRi33I,365
laion_clap/clap_module/model_configs/ViT-B-16.json,sha256=ZtSHZ6sRWR9K8tLRvyGEXD8r6hescOCYmZH_FmpTrlI,294
laion_clap/clap_module/model_configs/ViT-B-32-quickgelu.json,sha256=Q33GrMhFUmfCZG6E-ICgJtkawggo25sQCxil7boOdno,318
laion_clap/clap_module/model_configs/ViT-B-32.json,sha256=dD9PzuOuoXBcGGX4mjdn2BZRN2n1frSDqe6j5hDr5jM,294
laion_clap/clap_module/model_configs/ViT-L-14.json,sha256=YWdQqSfqjYsLjk7JMXnKs9-eTxUxy2DMXdaILLBjiIs,296
laion_clap/clap_module/openai.py,sha256=uQo2xxpAMO3mZttKLjlOHOzYfvMD-UhpifMSWp5fUzA,4724
laion_clap/clap_module/pann_model.py,sha256=UqT9pRAwo2aYA-09j8zwPXUJqxJNki4yy2IOBedLI-g,21221
laion_clap/clap_module/pretrained.py,sha256=y23UYtbiGbgFKt9ycRmIBcgIsUJEpi6sVCAY4f37Xaw,6212
laion_clap/clap_module/timm_model.py,sha256=Drv2xlMzkL52UcWVHHR09ZEPVPkU1A1siAr350Ap4_k,4300
laion_clap/clap_module/tokenizer.py,sha256=utweUiqTnhRfqrKupgxdCgFXYeaRdazkMt-z_DHsuJY,6204
laion_clap/clap_module/transform.py,sha256=BX3kP6VzLxMokTYpNS19CwDbinwOAhsqqIu_qwa4Y4o,872
laion_clap/clap_module/utils.py,sha256=OZzQ6MIJmKGqjbmuP-w_lJ4H4ClrZ1xJF_nPz8mBbtA,12988
laion_clap/clap_module/version.py,sha256=PmcQ2PI2oP8irnLtJLJby2YfW6sBvLAmL-VpABzTqwc,22
laion_clap/evaluate/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
laion_clap/evaluate/__pycache__/__init__.cpython-39.pyc,,
laion_clap/evaluate/__pycache__/eval_dcase.cpython-39.pyc,,
laion_clap/evaluate/__pycache__/eval_linear_probe.cpython-39.pyc,,
laion_clap/evaluate/__pycache__/eval_retrieval.cpython-39.pyc,,
laion_clap/evaluate/__pycache__/eval_retrieval_main.cpython-39.pyc,,
laion_clap/evaluate/__pycache__/eval_zeroshot_classification.cpython-39.pyc,,
laion_clap/evaluate/eval_dcase.py,sha256=fspgsPnf5MJtgYzoh_a1o9sTK7qayDCg4EjpOCckqPs,5455
laion_clap/evaluate/eval_linear_probe.py,sha256=I5tuYnBpuua8QbNQvddBQTuZ2NIyxX4shg1WBlyBl4s,20038
laion_clap/evaluate/eval_retrieval.py,sha256=D3LPErr3UGrxcqLzazoQq3_Mqpz4b3kMk_bTpUA8Sts,6718
laion_clap/evaluate/eval_retrieval_main.py,sha256=0igGymHfN2OI49VFjMZl0mYfKoJMGTsCzUe7z-p6vpM,9557
laion_clap/evaluate/eval_zeroshot_classification.py,sha256=cpbORg4kb7Ft8qdmN9P9bJ7W3bZYQMaXpyL4xx7JkgU,9769
laion_clap/hook.py,sha256=3ZNPHuzUu0FbcpROXT8lXMVxgoCPhJAtDZzAD66tLsg,8767
laion_clap/training/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
laion_clap/training/__pycache__/__init__.cpython-39.pyc,,
laion_clap/training/__pycache__/data.cpython-39.pyc,,
laion_clap/training/__pycache__/distributed.cpython-39.pyc,,
laion_clap/training/__pycache__/imagenet_zeroshot_data.cpython-39.pyc,,
laion_clap/training/__pycache__/infer_demo.cpython-39.pyc,,
laion_clap/training/__pycache__/logger.cpython-39.pyc,,
laion_clap/training/__pycache__/lp_main.cpython-39.pyc,,
laion_clap/training/__pycache__/lp_train.cpython-39.pyc,,
laion_clap/training/__pycache__/main.cpython-39.pyc,,
laion_clap/training/__pycache__/params.cpython-39.pyc,,
laion_clap/training/__pycache__/scheduler.cpython-39.pyc,,
laion_clap/training/__pycache__/train.cpython-39.pyc,,
laion_clap/training/__pycache__/zero_shot.cpython-39.pyc,,
laion_clap/training/audioset_textmap.npy,sha256=utoQMHDZL56t0z4bT0XshYP1kIDvIYyWa0MpS9TIbVs,84448
laion_clap/training/data.py,sha256=uVj8NKqcrEQQO2YzdMPEmu5FgN7IdAud0eFwVMjG5dw,32641
laion_clap/training/distributed.py,sha256=ESEmTfRalCj40VEChbah5H1eFd85D3NZTYcn27CkwRY,5078
laion_clap/training/imagenet_zeroshot_data.py,sha256=vNTT8FV4OVGiBXgvlMya_3Ok8tRX0w-Ww_JsvRrQjhs,22135
laion_clap/training/infer_demo.py,sha256=dAY4-rcnrS_4CQIU4ArkffVEWVxFHMjm4QMeHNrS2s0,3265
laion_clap/training/logger.py,sha256=2Q_60KQunY8bFa9qDbgZ4XjWqID4IDxqUTaIVs0m8-4,899
laion_clap/training/lp_main.py,sha256=PUCPGGtw0X0XQ9_prNjeUD65v_3iq6_1ARAISyBd8SE,25047
laion_clap/training/lp_train.py,sha256=nFuSoip4VU-cVQNbLWd7cTReyM32cafSzEut1KD2AEg,10645
laion_clap/training/main.py,sha256=GFnH4o3wWpTEWFUIgrsDosLTJDke_UG7cc1frEB-_rs,22608
laion_clap/training/params.py,sha256=7Ccs46C-Ca2P_XvJA6IVx-vJt7ENdhHODl50sR5AU-o,17401
laion_clap/training/scheduler.py,sha256=d3Z6mvVMHpDDhATjNLb2YCVCBKhKofCmoZm72hdlFBw,659
laion_clap/training/train.py,sha256=N-F7x7F6_A0cqG9FthUh9IDw2TOFwqYQIGqa7DF03BU,35672
laion_clap/training/zero_shot.py,sha256=Uw7FvPEKEcvWbuVhePc4HQoOnV_d4n_k9wiRq8_zpyg,3395
laion_clap/unit_test.py,sha256=YsZTMLSQqX6yiQLOiPLftPoBkYq9xj5VIt0UhC6231w,2597
tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tests/__pycache__/__init__.cpython-39.pyc,,
tests/__pycache__/check_ckpt.cpython-39.pyc,,
tests/__pycache__/check_tars.cpython-39.pyc,,
tests/__pycache__/data_loader_test.cpython-39.pyc,,
tests/check_ckpt.py,sha256=pV1-kQRXQKiOatxllrh30_JffcqjNZgxdPAQlVD9-98,56326
tests/check_tars.py,sha256=pco3GKN_e8m-KyiJO6mUznbq_yDymQuMcvfH6pbzL6k,3295
tests/data_loader_test.py,sha256=3QieDAYRojISmm4755bKpLh--i6ql0kXPu2fq5cjngo,2174
